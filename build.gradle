// 根项目配置，适用于所有子模块
buildscript {
    repositories {
        maven { url = 'https://maven.aliyun.com/nexus/content/groups/public/' }
        mavenCentral()
    }
    dependencies {
        classpath 'org.springframework.boot:spring-boot-gradle-plugin:3.5.3'
        classpath 'io.spring.gradle:dependency-management-plugin:1.0.15.RELEASE'
        classpath 'com.diffplug.spotless:spotless-plugin-gradle:6.21.0'
    }
}

// 所有项目通用配置
allprojects {
    repositories {
        maven { url = 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url = 'https://repository.aspose.com/repo' }
        mavenCentral()
    }
}

// 所有子项目通用配置
subprojects {
    // 跳过父项目
    if (project.name == 'nanfeng-parent') {
        return
    }

    apply plugin: 'java'
    apply plugin: 'com.diffplug.spotless'
    
    group = 'cn.bluesking'
    version = '2.2.0'

    // Java 版本配置
    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(17)
        }
    }

    // 通用依赖配置
    dependencies {
        // 使用 nanfeng-parent 作为依赖管理
        implementation platform(project(':nanfeng-parent'))
        
        // 日志依赖
        implementation 'org.slf4j:slf4j-api'
        
        // 测试依赖
        testImplementation 'org.junit.jupiter:junit-jupiter-api'
        testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
    }

    // 测试任务配置
    test {
        useJUnitPlatform()
    }

    // Spotless 配置
    spotless {
        // 通用配置
        encoding 'UTF-8'

        // Java 文件格式化
        java {
            target 'src/**/*.java'

            // 基本格式化规则
            importOrder('java', 'javax', 'jakarta', 'org', 'com', 'cn', '')
            
            // 以下是通用的格式化规则，不依赖于代码解析。
            // 移除行尾空格
            trimTrailingWhitespace()
            // 缩进使用 4 个空格
            indentWithSpaces(4)
            // 确保文件末尾有一个空行
            endWithNewline()
            
            // 行长度设置
            formatAnnotations()
            
            // 替换 Tab 为空格
            replaceRegex 'Tabs to spaces', '\t', '    '
            
            // 确保左大括号不换行
            replaceRegex 'Left curly on EOL', '(\\s+)\\{\n', '$1{\n'
            
            // 替换短分隔符注释为长分隔符注释
            custom 'Fix comment separators', { code ->
                return code.replaceAll('/\\*-{5,}\\s+(.*?)\\s+-{5,}\\*/', '/*-------------------- $1 --------------------*/')
            }

            // 移除 Java 类文件最后一个 } 右括号之后的换行符
            custom 'Remove newline after final closing brace', { code ->
                // 匹配文件末尾的 } 后面的换行符和空白字符
                return code.replaceAll('(\\})(\\s*\\n+)$', '$1')
            }

            // 将 Javadoc 中的 <strong> 标签替换为 <b> 标签
            custom 'Replace strong tags with b tags in Javadoc', { code ->
                // 处理 Javadoc 注释中的 <strong> 标签
                code = code.replaceAll('(/\\*\\*[\\s\\S]*?\\*/)', { match ->
                    String javadoc = match[0]
                    javadoc = javadoc.replaceAll('<strong>', '<b>')
                    javadoc = javadoc.replaceAll('</strong>', '</b>')
                    return javadoc
                })

                return code
            }
        }
    }
}

// 添加格式化任务
tasks.register('format') {
    dependsOn subprojects.collect { it.tasks.findByName('spotlessApply') }
}

// 在 build 之前检查格式
tasks.named('check').configure {
    dependsOn subprojects.collect { it.tasks.findByName('spotlessCheck') }
}