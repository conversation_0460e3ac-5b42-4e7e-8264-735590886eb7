plugins {
    id 'java'
    id 'org.springframework.boot'
}

description = '主应用模块'

dependencies {
    // 项目内部依赖
    implementation project(':nanfeng-common')
    implementation project(':nanfeng-http')
    implementation project(':nanfeng-geolocation')
    implementation project(':nanfeng-mailbox')
    implementation project(':nanfeng-notification')
    implementation project(':nanfeng-outlook')
    implementation project(':nanfeng-vpn')
    implementation project(':nanfeng-work')
    
    // 数据库
    implementation 'org.postgresql:postgresql'
    
    // 数据库迁移
    implementation 'org.flywaydb:flyway-core'
    implementation 'org.flywaydb:flyway-database-postgresql'
    
    // 测试
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
}

// 打包配置
bootJar {
    archiveBaseName = 'nanfeng-tools'
    archiveVersion = project.version
    mainClass = 'cn.bluesking.nanfeng.tools.Application'
}

// 确保 jar 任务不会创建普通的 jar
jar {
    enabled = false
} 