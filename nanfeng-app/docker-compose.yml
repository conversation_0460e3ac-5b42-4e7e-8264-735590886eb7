services:
  nanfeng-tools:
    image: maven.bluesking.cn/nanfeng-tools:${VERSION:-latest}
    container_name: nanfeng-tools
    restart: unless-stopped
    network_mode: host
    environment:
      - JAVA_OPT=-Djasypt.encryptor.password=nanfeng-tools -Xms512m -Xmx5120m -XX:+UseZGC -XX:ConcGCThreads=4 -XX:MaxDirectMemorySize=512m -Duser.timezone=Asia/Shanghai
      - SPRINGBOOT_OPT=--spring.profiles.active=prod  --logging.config=/app/config/logback-spring.xml
      - SPRING_CONFIG_LOCATION=/app/config/application.properties
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '6'
          memory: 6G
    ulimits:
      nofile:
        soft: 65535
        hard: 65535

volumes:
  data:
    driver: local
  config:
    driver: local
  logs:
    driver: local 