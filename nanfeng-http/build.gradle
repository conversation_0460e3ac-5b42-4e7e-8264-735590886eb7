apply plugin: 'java-library'
apply plugin: 'io.spring.dependency-management'

dependencies {
    // 依赖 nanfeng-common 模块
    api project(':nanfeng-common')
    
    // OkHttp 依赖
    api 'com.squareup.okhttp3:okhttp'
    api 'com.squareup.okhttp3:logging-interceptor'
    
    // Jackson 依赖
    api 'com.fasterxml.jackson.core:jackson-databind'
    
    // Reactor 依赖
    api 'io.projectreactor:reactor-core'
    
    // Spring 依赖
    compileOnly 'org.springframework:spring-context'
    
    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
}

bootJar {
    enabled = false
}

jar {
    enabled = true
} 