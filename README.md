# Nanfeng Tools

基于 Spring Boot 的多模块工具集合，提供 VPN 服务管理、地理位置服务、邮箱管理等功能。

## 项目结构

项目采用多模块结构，各模块职责如下：

```
nanfeng-tools/
├── nanfeng-common/           # 通用工具和基础组件模块
├── nanfeng-http/             # HTTP 客户端模块
├── nanfeng-geolocation/      # 地理位置服务模块
├── nanfeng-mailbox/          # 邮箱管理模块
├── nanfeng-notification/     # 通知服务模块
├── nanfeng-outlook/          # Outlook 邮件处理模块
├── nanfeng-vpn/              # VPN 服务管理模块
├── nanfeng-work/             # 工作工具模块
└── nanfeng-app/              # 主应用模块
```

详细的模块设计请参考 [多模块结构设计文档](docs/设计/模块设计/多模块结构设计.md)。

## 环境要求

- JDK 17+
- Gradle 8.0+
- Docker 20.10.0+ (用于容器化部署)
- Docker Compose 2.0.0+ (用于容器化部署)

## 构建与运行

### 构建项目

```bash
# 使用 Gradle Wrapper 构建
./gradlew clean build

# 或使用系统安装的 Gradle
gradle clean build
```

### 运行应用

```bash
# 使用 Gradle 运行
./gradlew :nanfeng-app:bootRun

# 或直接运行 JAR 文件
java -jar nanfeng-app/build/libs/nanfeng-tools-*.jar
```

### Docker 部署

```bash
# 构建 Docker 镜像
./gradlew :nanfeng-app:bootJar
docker build -t nanfeng-tools:latest -f nanfeng-app/Dockerfile nanfeng-app

# 使用 Docker Compose 运行
cd nanfeng-app
docker-compose up -d
```

详细的部署说明请参考 [Docker 部署指南](docs/指南/Docker-部署指南.md)。

## 开发指南

### 添加新模块

1. 在 `settings.gradle` 中添加模块定义
2. 创建模块目录和 `build.gradle` 文件
3. 定义模块依赖关系
4. 实现模块功能

### 代码规范

项目遵循 [通用编码规范](docs/规范/代码格式化规范.md) 和 [Java 编码规范](docs/规范/Java编码规范.md)。

可以使用以下命令检查和格式化代码：

```bash
# 检查代码格式
./gradlew checkFormat

# 格式化代码
./gradlew format
```

## 文档

- [项目文档目录](docs/README.md)
- [设计文档](docs/设计/)
- [使用指南](docs/指南/)
- [规范文档](docs/规范/)

## 许可证

[MIT License](LICENSE) 