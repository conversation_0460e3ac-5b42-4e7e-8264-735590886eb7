# 项目模块化重构设计

## 1. 概述

本文档描述了将 nanfeng-tools 单体项目重构为多模块结构的设计方案。重构的主要目标是提高代码的可维护性、可扩展性，并使各功能模块能够独立部署和演进。

### 1.1 重构目标

- 将单体应用拆分为多个功能独立的模块
- 提取公共代码到共享模块中，避免代码重复
- 明确模块间的依赖关系，降低耦合度
- 支持模块的独立部署和独立版本管理
- 保持现有功能的完整性和兼容性

### 1.2 模块划分原则

- **高内聚**：每个模块应当具有明确的职责边界
- **低耦合**：模块之间的依赖应当最小化
- **可重用**：公共功能应当提取到基础模块中
- **可测试**：模块应当便于单元测试和集成测试
- **可部署**：可执行模块应当能够独立部署

## 2. 模块结构设计

### 2.1 整体架构

重构后的项目将采用以下模块结构：

```
nanfeng-tools/
├── nanfeng-parent/                 # 父项目，管理依赖和构建配置
├── nanfeng-common/                 # 通用工具和基础组件
├── nanfeng-common-web/             # Web 相关的通用组件
├── nanfeng-http/                   # HTTP 客户端模块
├── nanfeng-geolocation/            # 地理位置服务模块
├── nanfeng-mailbox/                # 邮箱服务模块
├── nanfeng-outlook/                # Outlook 邮件处理模块
├── nanfeng-work/                   # 工作相关工具模块
└── nanfeng-server/                 # 服务器管理模块
```

详细的模块结构和依赖关系可参考[项目模块结构图](./项目模块结构图.md)。

### 2.2 模块职责

#### 2.2.1 nanfeng-parent

父项目，不包含实际代码，负责：
- 统一管理所有模块的依赖版本
- 提供公共的构建配置和插件
- 定义项目的整体版本号

#### 2.2.2 nanfeng-common

基础通用模块，提供：
- 通用工具类（如字符串处理、日期处理等）
- 基础异常类
- 通用数据模型
- 数据库相关工具（如枚举转换器）

#### 2.2.3 nanfeng-common-web

Web 相关的通用模块，提供：
- 全局异常处理
- 统一响应封装
- Web 相关工具类
- 通用拦截器和过滤器

#### 2.2.4 nanfeng-http

HTTP 客户端模块，提供：
- 基于 OkHttp 的响应式 HTTP 客户端
- 代理支持
- 请求/响应解析器
- 错误处理机制

#### 2.2.5 nanfeng-geolocation

地理位置服务模块，提供：
- IP 地址查询功能
- 多平台地理位置 API 集成
- 地址信息解析和标准化

#### 2.2.6 nanfeng-mailbox

邮箱服务模块，提供：
- 邮箱创建和管理
- 邮件转发规则配置
- 与 Posteio API 集成

#### 2.2.7 nanfeng-outlook

Outlook 邮件处理模块，提供：
- 邮件模板解析和生成
- 邮件信息提取
- Outlook 邮件格式处理

#### 2.2.8 nanfeng-work

工作相关工具模块，提供：
- EDI 记录处理
- Excel 数据处理
- 工作流相关功能

#### 2.2.9 nanfeng-server

服务器管理模块，提供：
- SSH 连接和命令执行
- 代理软件管理
- 防火墙配置
- 端口转发管理
- 服务器性能监控

## 3. 依赖关系

### 3.1 模块依赖图

```mermaid
graph TD
    parent[nanfeng-parent] --> common[nanfeng-common]
    parent --> common_web[nanfeng-common-web]
    parent --> http[nanfeng-http]
    parent --> geolocation[nanfeng-geolocation]
    parent --> mailbox[nanfeng-mailbox]
    parent --> outlook[nanfeng-outlook]
    parent --> work[nanfeng-work]
    parent --> server[nanfeng-server]
    
    common_web --> common
    http --> common
    geolocation --> common
    geolocation --> http
    geolocation --> common_web
    mailbox --> common
    mailbox --> http
    mailbox --> common_web
    outlook --> common
    outlook --> common_web
    work --> common
    work --> common_web
    server --> common
    server --> common_web
```

### 3.2 依赖说明

- **nanfeng-common**：基础模块，不依赖其他业务模块
- **nanfeng-common-web**：依赖 nanfeng-common
- **nanfeng-http**：依赖 nanfeng-common
- **业务模块**：依赖 nanfeng-common、nanfeng-common-web 和其他必要的基础模块

## 4. 构建配置

### 4.1 Gradle 多项目构建

使用 Gradle 的多项目构建功能，在根项目中配置 `settings.gradle`：

```groovy
rootProject.name = 'nanfeng-tools'

include 'nanfeng-parent'
include 'nanfeng-common'
include 'nanfeng-common-web'
include 'nanfeng-http'
include 'nanfeng-geolocation'
include 'nanfeng-mailbox'
include 'nanfeng-outlook'
include 'nanfeng-work'
include 'nanfeng-server'
```

### 4.2 父项目配置

在 `nanfeng-parent/build.gradle` 中配置公共依赖和插件：

```groovy
plugins {
    id 'java-platform'
    id 'maven-publish'
}

group = 'cn.bluesking'
version = '2.2.0'

javaPlatform {
    allowDependencies()
}

dependencies {
    constraints {
        // 定义依赖版本
        api 'org.springframework.boot:spring-boot-dependencies:3.5.3'
        api 'com.squareup.okhttp3:okhttp:4.12.0'
        // 其他依赖版本...
    }
}
```

### 4.3 子模块配置

每个子模块的 `build.gradle` 示例：

```groovy
plugins {
    id 'java'
    id 'org.springframework.boot' apply false
    id 'io.spring.dependency-management'
    id 'com.diffplug.spotless'
}

group = 'cn.bluesking'
version = '2.2.0'

// 应用父项目的依赖管理
dependencyManagement {
    imports {
        mavenBom "cn.bluesking:nanfeng-parent:${version}"
    }
}

dependencies {
    // 模块特定依赖
}
```

## 5. 部署配置

### 5.1 独立部署模块

可独立部署的模块包括：

- nanfeng-geolocation
- nanfeng-mailbox
- nanfeng-outlook
- nanfeng-work
- nanfeng-server

每个可独立部署的模块都应包含：

- 完整的 Spring Boot 应用配置
- 独立的 Dockerfile
- 独立的 docker-compose.yml
- 独立的配置文件

### 5.2 Docker 配置

每个可独立部署模块的 Dockerfile 示例：

```dockerfile
FROM openjdk:17-slim as builder
WORKDIR application
ARG JAR_FILE=build/libs/*.jar
COPY ${JAR_FILE} application.jar
RUN java -Djarmode=layertools -jar application.jar extract

FROM openjdk:17-slim
WORKDIR application

# 设置时区
ENV TIME_ZONE=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TIME_ZONE /etc/localtime && echo $TIME_ZONE > /etc/timezone

# JVM 和应用配置参数
ENV JAVA_OPT="-Xms500m -Xmx2048m -Duser.timezone=Asia/Shanghai"
ENV SPRINGBOOT_OPT="--spring.profiles.active=prod --logging.config=/app/config/logback-spring.xml"
ENV SPRING_CONFIG_LOCATION="optional:classpath:/application.properties,optional:file:/app/config/application.properties"

# 从构建阶段复制分层的 jar 文件
COPY --from=builder application/dependencies/ ./
COPY --from=builder application/spring-boot-loader/ ./
COPY --from=builder application/snapshot-dependencies/ ./
COPY --from=builder application/application/ ./

# 声明数据卷
VOLUME ["/app/config", "/app/data", "/app/logs"]

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["sh", "-c", "java ${JAVA_OPT} -Dspring.config.location=${SPRING_CONFIG_LOCATION} org.springframework.boot.loader.JarLauncher ${SPRINGBOOT_OPT}"]
```

### 5.3 Docker Compose 配置

每个可独立部署模块的 docker-compose.yml 示例：

```yaml
services:
  nanfeng-{module-name}:
    image: maven.bluesking.cn/nanfeng-{module-name}:latest
    container_name: nanfeng-{module-name}
    restart: unless-stopped
    network_mode: host
    environment:
      - JAVA_OPT=-Djasypt.encryptor.password=nanfeng-tools -Xms512m -Xmx2048m -XX:+UseZGC
      - SPRINGBOOT_OPT=--spring.profiles.active=prod --logging.config=/app/config/logback-spring.xml
      - SPRING_CONFIG_LOCATION=/app/config/application.properties
    volumes:
      - /opt/nanfeng-{module-name}/data:/app/data
      - /opt/nanfeng-{module-name}/config:/app/config
      - /opt/nanfeng-{module-name}/logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
```

## 6. 重构步骤

### 6.1 准备阶段

1. 创建新的项目结构
2. 设置 Gradle 多项目构建配置
3. 创建父项目和基础模块

### 6.2 代码迁移

1. 分析现有代码的依赖关系
2. 提取公共代码到基础模块
3. 按功能将代码迁移到相应模块
4. 调整包结构和导入语句

### 6.3 配置迁移

1. 拆分配置文件
2. 调整数据库配置
3. 配置模块间的通信机制

### 6.4 测试验证

1. 单元测试
2. 集成测试
3. 系统测试
4. 性能测试

### 6.5 部署验证

1. 构建各模块
2. 配置 Docker 镜像
3. 测试独立部署
4. 验证功能完整性

## 7. 风险与缓解措施

### 7.1 潜在风险

1. **功能中断**：重构过程可能导致现有功能暂时不可用
2. **性能影响**：模块间通信可能引入额外开销
3. **部署复杂性**：多模块部署比单体应用更复杂
4. **依赖冲突**：不同模块可能使用相同依赖的不同版本

### 7.2 缓解措施

1. **功能中断**：
   - 采用渐进式重构
   - 保持完整的测试覆盖
   - 在非生产环境先验证

2. **性能影响**：
   - 优化模块间通信
   - 监控性能指标
   - 必要时调整架构

3. **部署复杂性**：
   - 完善部署文档
   - 自动化部署流程
   - 提供统一的部署脚本

4. **依赖冲突**：
   - 在父项目中统一管理依赖版本
   - 使用 BOM（Bill of Materials）
   - 定期审查依赖

## 8. 时间规划

| 阶段 | 工作内容 | 预计时间 |
|------|---------|---------|
| 准备 | 创建项目结构、配置构建系统 | 1 周 |
| 基础模块开发 | 提取公共代码、创建基础模块 | 2 周 |
| 业务模块迁移 | 拆分业务代码到各模块 | 3 周 |
| 测试与修复 | 单元测试、集成测试、修复问题 | 2 周 |
| 部署验证 | 配置部署环境、验证功能 | 1 周 |
| 文档完善 | 更新设计文档、使用指南 | 1 周 |

## 9. 结论

通过将 nanfeng-tools 单体项目重构为多模块结构，可以显著提高代码的可维护性和可扩展性，使各功能模块能够独立演进和部署。虽然重构过程存在一定风险，但通过合理的规划和缓解措施，可以确保平稳过渡。

重构完成后，项目将具有更清晰的结构、更明确的模块边界和更灵活的部署选项，为未来的功能扩展和性能优化奠定基础。 