# 项目模块结构图

## 模块结构与依赖关系

下图展示了 nanfeng-tools 项目的模块结构和依赖关系：

```mermaid
graph TD
    subgraph "项目结构"
        root[nanfeng-tools] --> parent[nanfeng-parent]
        root --> common[nanfeng-common]
        root --> common_web[nanfeng-common-web]
        root --> http[nanfeng-http]
        root --> geolocation[nanfeng-geolocation]
        root --> mailbox[nanfeng-mailbox]
        root --> outlook[nanfeng-outlook]
        root --> work[nanfeng-work]
        root --> server[nanfeng-server]
    end
    
    subgraph "依赖关系"
        common_web --> common
        http --> common
        
        geolocation --> common
        geolocation --> common_web
        geolocation --> http
        
        mailbox --> common
        mailbox --> common_web
        mailbox --> http
        
        outlook --> common
        outlook --> common_web
        
        work --> common
        work --> common_web
        
        server --> common
        server --> common_web
    end
    
    subgraph "部署单元"
        style geolocation fill:#f9f,stroke:#333,stroke-width:2px
        style mailbox fill:#f9f,stroke:#333,stroke-width:2px
        style outlook fill:#f9f,stroke:#333,stroke-width:2px
        style work fill:#f9f,stroke:#333,stroke-width:2px
        style server fill:#f9f,stroke:#333,stroke-width:2px
    end
    
    classDef library fill:#cfc,stroke:#333,stroke-width:1px
    class common,common_web,http library
```

## 图例说明

- **绿色节点**：基础库模块，不可独立部署
- **粉色节点**：业务模块，可独立部署
- **箭头**：表示模块间的依赖关系

## 模块分类

### 基础库模块

- **nanfeng-parent**：父项目，管理依赖和构建配置
- **nanfeng-common**：通用工具和基础组件
- **nanfeng-common-web**：Web 相关的通用组件
- **nanfeng-http**：HTTP 客户端模块

### 可独立部署的业务模块

- **nanfeng-geolocation**：地理位置服务模块
- **nanfeng-mailbox**：邮箱服务模块
- **nanfeng-outlook**：Outlook 邮件处理模块
- **nanfeng-work**：工作相关工具模块
- **nanfeng-server**：服务器管理模块 