# nanfeng-parent 模块说明

## 1. 概述

`nanfeng-parent` 是 nanfeng-tools 项目的父模块，它不包含任何实际的业务代码，而是作为一个依赖管理和构建配置的中央控制点。该模块采用了 Maven/Gradle 的继承机制，为所有子模块提供统一的依赖版本管理和构建规则，确保整个项目的一致性和可维护性。

## 2. 主要功能

### 2.1 依赖版本管理

`nanfeng-parent` 模块使用 Gradle 的 `java-platform` 插件，通过 BOM (Bill of Materials) 机制统一管理项目中所有依赖的版本。这样做的好处包括：

- **版本一致性**：确保所有子模块使用相同版本的依赖，避免版本冲突
- **集中管理**：在一个地方管理所有依赖版本，便于升级和维护
- **简化配置**：子模块不需要指定依赖版本，减少了配置的复杂性

例如，当需要升级 Spring Boot 版本时，只需在 `nanfeng-parent` 中修改一处即可应用到所有子模块。

### 2.2 构建规则统一

通过父模块可以定义统一的构建规则和插件配置，包括但不限于：

- Java 版本设置
- 编译选项
- 代码风格检查规则
- 测试配置
- 打包规则

这确保了所有模块遵循相同的开发标准和构建流程。

### 2.3 项目元数据管理

`nanfeng-parent` 模块还负责定义整个项目的基本元数据，如：

- 组织 ID (group)
- 版本号 (version)
- 项目描述
- 许可证信息
- 开发者信息

## 3. 技术实现

### 3.1 使用的插件

`nanfeng-parent` 模块使用了以下 Gradle 插件：

```groovy
plugins {
    id 'java-platform'  // 用于创建平台依赖
    id 'maven-publish'  // 用于发布到 Maven 仓库
}
```

- `java-platform`：提供依赖约束管理功能，类似于 Maven 的 BOM
- `maven-publish`：支持将构建产物发布到 Maven 仓库

### 3.2 依赖约束配置

在 `dependencies` 块中，使用 `constraints` 定义依赖版本：

```groovy
dependencies {
    api platform('org.springframework.boot:spring-boot-dependencies:3.5.3')
    
    constraints {
        api 'com.aventrix.jnanoid:jnanoid:2.0.0'
        api 'com.google.code.gson:gson:2.9.1'
        // 更多依赖版本...
    }
}
```

这里使用了 Spring Boot 的依赖管理功能，并添加了项目特定的依赖约束。

### 3.3 发布配置

配置了将平台依赖发布到本地或远程 Maven 仓库：

```groovy
publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.javaPlatform
        }
    }
    repositories {
        maven {
            url = "${project.rootDir}/repo"
        }
    }
}
```

## 4. 与子模块的关系

### 4.1 子模块如何使用父模块

子模块通过以下方式引用 `nanfeng-parent` 提供的依赖管理：

```groovy
dependencies {
    // 使用 nanfeng-parent 作为依赖管理
    implementation platform(project(':nanfeng-parent'))
    
    // 引用依赖时不需要指定版本
    implementation 'org.slf4j:slf4j-api'
}
```

### 4.2 依赖解析流程

1. 子模块声明对 `nanfeng-parent` 的依赖
2. 子模块引用某个库时不指定版本
3. Gradle 从 `nanfeng-parent` 中查找该库的版本约束
4. 使用找到的版本约束解析实际依赖

## 5. 为什么不直接在根目录的 build.gradle 中配置依赖版本？

虽然可以将依赖版本管理放在项目根目录的 `build.gradle` 中，但使用独立的 `nanfeng-parent` 模块有以下几个显著优势：

### 5.1 模块化和关注点分离

- **职责单一**：`nanfeng-parent` 只负责依赖管理，根 `build.gradle` 则专注于构建流程和通用配置
- **关注点分离**：将依赖版本定义与构建逻辑分开，使配置更清晰
- **模块化思想**：符合模块化设计原则，每个组件只做一件事并做好它

### 5.2 可重用性和可移植性

- **可发布性**：`nanfeng-parent` 可以作为独立构件发布到 Maven 仓库
- **跨项目共享**：多个相关项目可以共用同一个 parent 模块
- **版本控制**：parent 模块可以有自己的版本演进，不影响使用它的项目

### 5.3 标准化和行业实践

- **行业标准**：遵循 Maven/Gradle 的标准项目结构和最佳实践
- **清晰的依赖图**：明确表达了模块间的依赖关系
- **符合预期**：对有 Maven/Gradle 经验的开发者来说更易理解

### 5.4 灵活性和可扩展性

- **独立演进**：parent 模块可以独立于应用程序演进
- **条件引用**：子模块可以选择性地使用或不使用 parent 模块
- **多级继承**：支持更复杂的继承层次，如企业级 → 部门级 → 项目级 parent

### 5.5 实际案例对比

#### 根 build.gradle 方式

```groovy
// 根 build.gradle
allprojects {
    // 版本定义与构建逻辑混合在一起
    ext {
        springBootVersion = '3.5.3'
        okhttpVersion = '4.12.0'
        // 更多版本...
    }
    
    // 构建逻辑
    // ...
}
```

#### nanfeng-parent 方式

```groovy
// nanfeng-parent/build.gradle
dependencies {
    api platform('org.springframework.boot:spring-boot-dependencies:3.5.3')
    constraints {
        api 'com.squareup.okhttp3:okhttp:4.12.0'
        // 更多版本...
    }
}

// 子模块 build.gradle
dependencies {
    implementation platform(project(':nanfeng-parent'))
    implementation 'com.squareup.okhttp3:okhttp' // 无需指定版本
}
```

nanfeng-parent 方式更清晰地表达了"这些是依赖版本约束"的意图，而不是简单的变量定义。

## 6. 最佳实践

### 6.1 何时更新父模块

以下情况需要更新 `nanfeng-parent` 模块：

- 升级核心框架版本（如 Spring Boot）
- 添加新的公共依赖
- 更新现有依赖版本
- 修改全局构建配置

### 6.2 版本管理策略

- 使用语义化版本号 (Semantic Versioning)
- 主要版本更新时同步更新所有子模块
- 为重大变更创建详细的升级指南

### 6.3 依赖冲突处理

当出现依赖冲突时，可以在 `nanfeng-parent` 中通过以下方式解决：

- 明确指定传递依赖的版本
- 排除冲突的传递依赖
- 强制使用特定版本

## 7. 总结

`nanfeng-parent` 模块是项目的"基石"，通过集中管理依赖版本和构建配置，它确保了整个项目的一致性和可维护性。虽然这个模块不包含任何业务逻辑，但它对于多模块项目的健康发展至关重要，是实现"一处修改，处处生效"的关键机制。

通过合理维护和更新 `nanfeng-parent` 模块，可以显著降低依赖管理的复杂性，提高项目的可维护性和稳定性。 