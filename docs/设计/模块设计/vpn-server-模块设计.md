# VPN Server 模块设计

## 1. 概述

VPN 模块下的 Server 子模块主要负责服务器相关操作，为其他模块提供服务器管理、代理软件部署和配置、防火墙管理、端口转发、服务器基准测试等功能。本模块设计旨在实现功能模块化、实现与接口分离、可扩展性高的系统架构。

### 1.1 设计目标

- 提供统一的服务器管理接口，简化服务器操作
- 支持多种代理软件的安装、配置和管理
- 提供防火墙规则的便捷管理方式
- 实现灵活的端口转发功能
- 提供服务器性能基准测试功能
- 确保模块间松耦合，便于扩展和维护

### 1.2 功能范围

- 服务器基本信息管理（添加、删除、查询）
- SSH 连接和命令执行
- 代理软件（V2Ray、Shadowsocks、Hysteria2 等）的安装和配置
- 防火墙管理（开放/关闭端口）
- 端口转发配置
- 服务器基准测试和性能监控

## 2. 架构设计

### 2.1 整体架构

模块位于 `cn.bluesking.nanfeng.tools.server.server` 包下，采用分层架构：

```
cn.bluesking.nanfeng.tools.server.server
├── ssh                  // SSH 连接和命令执行
│   ├── model            // SSH 相关模型和枚举
│   ├── service          // SSH 服务接口
│   │   └── impl         // SSH 服务实现
│   └── util             // SSH 工具类
├── proxy                // 代理软件管理
│   ├── infrastructure   // 不同代理实现
│   │   ├── v2ray        
│   │   ├── shadowsocks  
│   │   ├── hysteria2    
│   │   └── ss_v2plugin  
│   ├── model            // 代理相关模型和枚举
│   └── service          // 代理服务接口
│       └── impl         // 代理服务实现
├── firewall             // 防火墙管理
│   ├── infrastructure   // 不同防火墙实现
│   │   ├── firewalld    
│   │   └── iptables     
│   ├── model            // 防火墙相关模型和枚举
│   └── service          // 防火墙服务接口
│       └── impl         // 防火墙服务实现
├── portforward          // 端口转发管理
│   ├── infrastructure   // 不同转发实现
│   │   ├── firewalld    
│   │   ├── socat        
│   │   └── nginx        
│   ├── model            // 转发相关模型和枚举
│   └── service          // 转发服务接口
│       └── impl         // 转发服务实现
├── benchmark            // 基准测试管理
│   ├── infrastructure   // 不同测试实现
│   ├── model            // 测试相关模型和枚举
│   └── service          // 测试服务接口
│       └── impl         // 测试服务实现
└── server               // 服务器管理
    ├── model            // 服务器相关模型和枚举
    └── service          // 服务器服务接口
        └── impl         // 服务器服务实现
```

### 2.2 设计原则

- **职责分离**：各功能模块相互独立，只通过接口交互
- **接口隔离**：为各功能定义清晰的接口，实现与接口分离
- **依赖注入**：通过 Spring 管理依赖，降低组件间耦合度
- **策略模式**：针对不同实现（如不同代理软件）使用策略模式实现切换
- **Repository 模式**：数据访问通过 Repository 接口封装，实现数据访问与业务逻辑分离

### 2.3 技术选型

- **SSH 连接**：使用 Apache MINA SSHD 库实现 SSH 连接和命令执行
- **配置管理**：采用 JSON 或 YAML 格式存储配置信息
- **数据存储**：使用 Spring Data JPA 访问数据库
- **依赖管理**：使用 Spring 框架的依赖注入机制
- **异步处理**：使用 CompletableFuture 处理异步操作

## 3. 核心接口

### 3.1 服务器管理接口

```java
public interface ServerService {
    ServerInfo addServer(ServerInfo serverInfo);
    boolean removeServer(Long serverId);
    List<ServerInfo> getAllServers();
    ServerInfo getServerById(Long serverId);
    ExecutionResult executeCommand(Long serverId, String command);
    Map<Long, ExecutionResult> batchExecuteCommand(List<Long> serverIds, String command);
    boolean saveServerKey(Long serverId, String privateKey);
    boolean removeServerKey(Long serverId);
    boolean checkServerConnection(Long serverId);
    ServerStatus updateServerStatus(Long serverId);
    Map<Long, ServerStatus> batchUpdateServerStatus(List<Long> serverIds);
}
```

### 3.2 SSH 服务接口

```java
public interface SshService {
    SshSession connect(String host, int port, String username, String password);
    SshSession connectWithKey(String host, int port, String username, String privateKey);
    ExecutionResult executeCommand(SshSession session, String command);
    ExecutionResult executeCommandWithTimeout(SshSession session, String command, long timeout);
    boolean uploadFile(SshSession session, String localPath, String remotePath);
    boolean downloadFile(SshSession session, String remotePath, String localPath);
    boolean uploadText(SshSession session, String content, String remotePath);
    String downloadText(SshSession session, String remotePath);
    void disconnect(SshSession session);
    boolean isConnected(SshSession session);
}
```

### 3.3 代理软件管理接口

```java
public interface ProxyService {
    boolean install(Long serverId, ProxyConfig config);
    boolean update(Long serverId, String proxyId);
    boolean configure(Long serverId, String proxyId, ProxyConfig config);
    boolean start(Long serverId, String proxyId);
    boolean stop(Long serverId, String proxyId);
    boolean uninstall(Long serverId, String proxyId);
    ProxyStatus getStatus(Long serverId, String proxyId);
    List<ProxyInfo> getProxiesByServer(Long serverId);
    boolean exportConfig(Long serverId, String proxyId, String format);
    boolean generateQRCode(Long serverId, String proxyId, String filePath);
}
```

### 3.4 防火墙管理接口

```java
public interface FirewallService {
    boolean openPort(Long serverId, int port, String protocol);
    boolean closePort(Long serverId, int port, String protocol);
    List<PortInfo> getOpenedPorts(Long serverId);
    boolean isPortOpen(Long serverId, int port, String protocol);
    boolean restartFirewall(Long serverId);
    boolean autoDetectAndSetFirewall(Long serverId);
    boolean addRichRule(Long serverId, String rule);
    boolean removeRichRule(Long serverId, String rule);
    FirewallType detectFirewallType(Long serverId);
}
```

### 3.5 端口转发管理接口

```java
public interface PortForwardService {
    boolean addForward(Long serverId, PortForwardConfig config);
    boolean removeForward(Long serverId, String forwardId);
    boolean updateForward(Long serverId, PortForwardConfig config);
    boolean enableForward(Long serverId, String forwardId);
    boolean disableForward(Long serverId, String forwardId);
    List<PortForwardConfig> getAllForwards(Long serverId);
    PortForwardConfig getForward(Long serverId, String forwardId);
    boolean checkForwardStatus(Long serverId, String forwardId);
    ForwardType detectBestForwardMethod(Long serverId);
}
```

### 3.6 基准测试管理接口

```java
public interface BenchmarkService {
    BenchmarkResult runBenchmark(Long serverId, BenchmarkType type);
    List<BenchmarkResult> getBenchmarkHistory(Long serverId);
    BenchmarkResult getLatestBenchmark(Long serverId, BenchmarkType type);
    boolean saveLatencyData(Long serverId, LatencyData data);
    LatencyData getLatestLatencyData(Long serverId);
    boolean schedulePeriodicalBenchmark(Long serverId, BenchmarkType type, int interval);
    boolean cancelScheduledBenchmark(Long serverId, BenchmarkType type);
    Map<String, Object> getServerPerformanceMetrics(Long serverId);
}
```

## 4. 类设计

### 4.1 核心模型类

#### 4.1.1 服务器信息

```java
@Entity
@Table(name = "server_info")
public class ServerInfo {
    @Id
    private Long id;
    private String name;
    private String host;
    private int port;
    private String username;
    @JsonIgnore
    private String password;
    @JsonIgnore
    private String privateKey;
    @Enumerated(EnumType.STRING)
    private ServerStatus status;
    private LocalDateTime lastCheckTime;
    private String osType;
    private String osVersion;
    private String cpuInfo;
    private String memoryInfo;
    private String diskInfo;
    private boolean autoReconnect;
    private int connectionTimeout;
    @OneToMany(mappedBy = "serverId", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ProxyInfo> proxies;
    // getter/setter...
}
```

#### 4.1.2 SSH 会话

```java
public class SshSession {
    private Session session;
    private ChannelExec channel;
    private String host;
    private int port;
    private boolean connected;
    private LocalDateTime connectTime;
    private LocalDateTime lastActiveTime;
    private int timeoutMillis;
    // getter/setter...
}
```

#### 4.1.3 命令执行结果

```java
public class ExecutionResult {
    private int exitCode;
    private String stdout;
    private String stderr;
    private long executionTime;
    private boolean timedOut;
    private boolean successful;
    
    public boolean isSuccessful() {
        return exitCode == 0 && !timedOut;
    }
    
    // getter/setter...
}
```

#### 4.1.4 代理配置

```java
public abstract class ProxyConfig {
    private String id;
    private ProxyType type;
    private int port;
    private boolean enableTls;
    private String remark;
    private boolean autoStart;
    // 通用字段...
    
    // 抽象方法，转换为配置内容
    public abstract String toConfigString();
}

public class V2RayConfig extends ProxyConfig {
    private String protocol; // vmess, vless等
    private String uuid;
    private String network;  // tcp, ws等
    private String path;
    private String host;
    private String security; // none, tls等
    // V2Ray特有字段...
    
    @Override
    public String toConfigString() {
        // 生成V2Ray配置JSON...
    }
}

public class ShadowsocksConfig extends ProxyConfig {
    private String method;
    private String password;
    // Shadowsocks特有字段...
    
    @Override
    public String toConfigString() {
        // 生成Shadowsocks配置...
    }
}

public class Hysteria2Config extends ProxyConfig {
    private int uploadMbps;
    private int downloadMbps;
    private String password;
    // Hysteria2特有字段...
    
    @Override
    public String toConfigString() {
        // 生成Hysteria2配置...
    }
}
```

### 4.2 策略类设计

#### 4.2.1 代理处理器

```java
public interface ProxyHandler {
    ProxyType getType();
    boolean install(SshSession session, ProxyConfig config);
    boolean update(SshSession session);
    boolean configure(SshSession session, ProxyConfig config);
    boolean start(SshSession session);
    boolean stop(SshSession session);
    boolean uninstall(SshSession session);
    ProxyStatus getStatus(SshSession session);
    String getConfigFilePath(SshSession session);
    String getVersion(SshSession session);
}
```

#### 4.2.2 防火墙处理器

```java
public interface FirewallHandler {
    FirewallType getType();
    boolean openPort(SshSession session, int port, String protocol);
    boolean closePort(SshSession session, int port, String protocol);
    List<PortInfo> getOpenedPorts(SshSession session);
    boolean isPortOpen(SshSession session, int port, String protocol);
    boolean restart(SshSession session);
    boolean addRichRule(SshSession session, String rule);
    boolean removeRichRule(SshSession session, String rule);
    boolean isInstalled(SshSession session);
    boolean install(SshSession session);
}
```

#### 4.2.3 端口转发处理器

```java
public interface ForwardHandler {
    ForwardType getType();
    boolean addForward(SshSession session, PortForwardConfig config);
    boolean removeForward(SshSession session, PortForwardConfig config);
    boolean updateForward(SshSession session, PortForwardConfig config);
    boolean enableForward(SshSession session, PortForwardConfig config);
    boolean disableForward(SshSession session, PortForwardConfig config);
    boolean checkStatus(SshSession session, PortForwardConfig config);
    boolean isAvailable(SshSession session);
}
```

#### 4.2.4 基准测试处理器

```java
public interface BenchmarkHandler {
    BenchmarkType getType();
    BenchmarkResult runBenchmark(SshSession session);
    boolean isSupported(SshSession session);
    String getCommandString();
    BenchmarkResult parseResult(String output);
}
```

## 5. 数据模型

### 5.1 数据库实体

- **ServerInfo**：服务器信息
- **ProxyInfo**：代理信息
- **FirewallConfig**：防火墙配置
- **PortForwardConfig**：端口转发配置
- **BenchmarkResult**：基准测试结果
- **LatencyData**：延迟数据
- **ScheduledTask**：计划任务信息

### 5.2 枚举类型

```java
// 代理类型
public enum ProxyType {
    V2RAY,
    SHADOWSOCKS,
    HYSTERIA2,
    SHADOWSOCKS_V2RAY_PLUGIN,
    TROJAN,
    XRAY
}

// 防火墙类型
public enum FirewallType {
    FIREWALLD,
    IPTABLES,
    UFW,
    NONE
}

// 端口转发类型
public enum ForwardType {
    FIREWALLD,
    SOCAT,
    NGINX,
    IPTABLES,
    HAPROXY
}

// 基准测试类型
public enum BenchmarkType {
    HARDWARE_INFO,
    DISK_SPEED,
    NETWORK_SPEED,
    LATENCY,
    CPU_STRESS,
    MEMORY_TEST,
    FULL_TEST
}

// 服务器状态
public enum ServerStatus {
    ONLINE,
    OFFLINE,
    UNKNOWN,
    CONNECTING,
    MAINTENANCE
}

// 操作系统类型
public enum OsType {
    CENTOS,
    UBUNTU,
    DEBIAN,
    FEDORA,
    ALPINE,
    OTHER
}
```

## 6. 实现策略

### 6.1 依赖注入和接口隔离

使用 Spring 框架的依赖注入机制，通过接口隔离不同的实现：

```java
@Configuration
public class ServerModuleConfig {
    @Bean
    public SshService sshService() {
        return new SshServiceImpl();
    }
    
    @Bean
    public ServerService serverInfoService(SshService sshService, ServerRepository repository) {
        return new ServerServiceImpl(sshService, repository);
    }
    
    @Bean
    public ProxyService proxyService(SshService sshService, List<ProxyHandler> handlers) {
        return new ProxyServiceImpl(sshService, handlers);
    }
    
    // 其他Bean配置...
}
```

### 6.2 策略模式和处理器注册

使用策略模式处理不同类型的实现，由 Spring 自动收集和注册处理器：

```java
@Service
public class ProxyServiceImpl implements ProxyService {
    private final SshService sshService;
    private final Map<ProxyType, ProxyHandler> proxyHandlers;
    
    @Autowired
    public ProxyServiceImpl(SshService sshService, List<ProxyHandler> handlers) {
        this.sshService = sshService;
        this.proxyHandlers = handlers.stream()
            .collect(Collectors.toMap(ProxyHandler::getType, h -> h));
    }
    
    @Override
    public boolean install(String serverId, ProxyConfig config) {
        ServerInfo server = serverRepository.findById(serverId)
            .orElseThrow(() -> new ServerNotFoundException("Server not found: " + serverId));
        
        ProxyHandler handler = proxyHandlers.get(config.getType());
        if (handler == null) {
            throw new UnsupportedProxyTypeException("Unsupported proxy type: " + config.getType());
        }
        
        try (SshSession session = sshService.connectWithKey(
                server.getHost(), server.getPort(), server.getUsername(), server.getPrivateKey())) {
            return handler.install(session, config);
        } catch (Exception e) {
            throw new ProxyOperationException("Failed to install proxy", e);
        }
    }
    
    // 其他方法实现...
}
```

### 6.3 异常处理

所有模块相关异常继承自 BusinessException：

```java
// 基础异常类
public class ServerModuleException extends BusinessException {
    public ServerModuleException(String message) {
        super(message);
    }
    
    public ServerModuleException(String message, Throwable cause) {
        super(500, message, cause);
    }
}

// 具体异常
public class ServerNotFoundException extends ServerModuleException {
    public ServerNotFoundException(String serverId) {
        super("服务器未找到：" + serverId);
    }
}

public class SshConnectionException extends ServerModuleException {
    public SshConnectionException(String host, Throwable cause) {
        super("SSH 连接失败：" + host, cause);
    }
}

public class ProxyOperationException extends ServerModuleException {
    public ProxyOperationException(String message) {
        super(message);
    }
    
    public ProxyOperationException(String message, Throwable cause) {
        super(message, cause);
    }
}

public class FirewallOperationException extends ServerModuleException {
    public FirewallOperationException(String message) {
        super(message);
    }
    
    public FirewallOperationException(String message, Throwable cause) {
        super(message, cause);
    }
}
```

### 6.4 批量操作实现

实现服务器批量操作，提高效率：

```java
@Service
public class ServerServiceImpl implements ServerService {
    @Override
    public Map<String, ExecutionResult> batchExecuteCommand(List<String> serverIds, String command) {
        Map<String, ExecutionResult> results = new ConcurrentHashMap<>();
        
        // 并行执行命令
        serverIds.parallelStream().forEach(serverId -> {
            try {
                ExecutionResult result = executeCommand(serverId, command);
                results.put(serverId, result);
            } catch (Exception e) {
                logger.error("执行命令失败：serverId={}, command={}", serverId, command, e);
                results.put(serverId, new ExecutionResult(-1, "", e.getMessage()));
            }
        });
        
        return results;
    }
    
    @Override
    public Map<String, ServerStatus> batchUpdateServerStatus(List<String> serverIds) {
        Map<String, ServerStatus> results = new ConcurrentHashMap<>();
        
        // 并行更新状态
        serverIds.parallelStream().forEach(serverId -> {
            try {
                ServerStatus status = updateServerStatus(serverId);
                results.put(serverId, status);
            } catch (Exception e) {
                logger.error("更新服务器状态失败：serverId={}", serverId, e);
                results.put(serverId, ServerStatus.UNKNOWN);
            }
        });
        
        return results;
    }
}
```

### 6.5 资源管理

所有连接资源使用 try-with-resources 确保正确关闭：

```java
public ExecutionResult executeCommand(String serverId, String command) {
    ServerInfo server = getServerById(serverId);
    if (server == null) {
        throw new ServerNotFoundException(serverId);
    }
    
    try (SshSession session = sshService.connectWithKey(
            server.getHost(), server.getPort(), server.getUsername(), server.getPrivateKey())) {
        return sshService.executeCommand(session, command);
    } catch (Exception e) {
        throw new SshExecutionException("命令执行失败", e);
    }
}
```

### 6.6 自动类型探测

针对防火墙和操作系统等环境因素，实现自动探测功能：

```java
@Service
public class FirewallServiceImpl implements FirewallService {
    @Override
    public FirewallType detectFirewallType(String serverId) {
        try {
            ExecutionResult result = serverInfoService.executeCommand(
                    serverId, "which firewall-cmd iptables ufw | head -1");
            if (result.getStdout().contains("firewall-cmd")) {
                return FirewallType.FIREWALLD;
            } else if (result.getStdout().contains("iptables")) {
                return FirewallType.IPTABLES;
            } else if (result.getStdout().contains("ufw")) {
                return FirewallType.UFW;
            } else {
                return FirewallType.NONE;
            }
        } catch (Exception e) {
            logger.error("防火墙类型探测失败", e);
            return FirewallType.NONE;
        }
    }
}
```

## 7. 扩展性设计

### 7.1 新代理软件支持

添加新的代理软件只需实现 ProxyHandler 接口：

```java
@Component
public class Hysteria2ProxyHandler implements ProxyHandler {
    @Override
    public ProxyType getType() {
        return ProxyType.HYSTERIA2;
    }
    
    @Override
    public boolean install(SshSession session, ProxyConfig config) {
        // 1. 下载 Hysteria2 二进制文件
        // 2. 设置权限
        // 3. 创建配置文件
        // 4. 设置系统服务
        // ...实现逻辑
    }
    
    // 实现其他接口方法...
}
```

### 7.2 新防火墙类型支持

添加新的防火墙类型只需实现 FirewallHandler 接口：

```java
@Component
public class UfwFirewallHandler implements FirewallHandler {
    @Override
    public FirewallType getType() {
        return FirewallType.UFW;
    }
    
    @Override
    public boolean openPort(SshSession session, int port, String protocol) {
        // 实现 UFW 开放端口的命令
        String command = String.format("ufw allow %d/%s", port, protocol.toLowerCase());
        ExecutionResult result = sshService.executeCommand(session, command);
        return result.isSuccessful();
    }
    
    // 实现其他接口方法...
}
```

### 7.3 新端口转发方式

添加新的端口转发方式只需实现 ForwardHandler 接口：

```java
@Component
public class HaproxyForwardHandler implements ForwardHandler {
    @Override
    public ForwardType getType() {
        return ForwardType.HAPROXY;
    }
    
    @Override
    public boolean addForward(SshSession session, PortForwardConfig config) {
        // 1. 检查 HAProxy 是否安装
        // 2. 添加配置到 HAProxy 配置文件
        // 3. 重新加载 HAProxy 服务
        // ...实现逻辑
    }
    
    // 实现其他接口方法...
}
```

### 7.4 新的基准测试类型

添加新的基准测试类型只需实现 BenchmarkHandler 接口：

```java
@Component
public class NetworkBenchmarkHandler implements BenchmarkHandler {
    @Override
    public BenchmarkType getType() {
        return BenchmarkType.NETWORK_SPEED;
    }
    
    @Override
    public BenchmarkResult runBenchmark(SshSession session) {
        // 1. 执行网络测试命令
        // 2. 解析结果
        // 3. 返回基准测试结果
        // ...实现逻辑
    }
    
    // 实现其他接口方法...
}
```

## 8. 安全性设计

### 8.1 密码和密钥管理

- 所有敏感信息（密码、密钥）在数据库中加密存储
- 密钥文件存储在安全位置，并限制访问权限
- 支持密钥轮换机制

```java
@Service
public class EncryptionService {
    private final String secretKey;
    
    public EncryptionService(@Value("${app.encryption.key}") String secretKey) {
        this.secretKey = secretKey;
    }
    
    public String encrypt(String text) {
        // 使用 AES 加密
    }
    
    public String decrypt(String encryptedText) {
        // 使用 AES 解密
    }
}
```

### 8.2 权限控制

- 操作日志记录所有关键操作
- 基于角色的访问控制
- 服务器操作前权限检查

```java
@Aspect
@Component
public class OperationLogAspect {
    @Around("execution(* cn.bluesking.nanfeng.tools.serverver.base.*.service.*.*(..))")
    public Object logOperation(ProceedingJoinPoint joinPoint) throws Throwable {
        // 记录操作日志
    }
}
```

### 8.3 连接安全

- SSH 连接超时控制
- 支持 SSH 密钥认证
- 命令注入防护

```java
public class CommandSanitizer {
    public static String sanitize(String command) {
        // 过滤危险字符和命令注入风险
    }
}
```

## 9. 性能优化

### 9.1 连接池

为提高性能，实现 SSH 连接池：

```java
@Service
public class SshConnectionPool {
    private final Map<String, Queue<SshSession>> sessionPool = new ConcurrentHashMap<>();
    
    public SshSession borrowSession(String serverKey, Supplier<SshSession> sessionSupplier) {
        // 从连接池获取或创建连接
    }
    
    public void returnSession(String serverKey, SshSession session) {
        // 将连接返回连接池
    }
    
    public void invalidateSession(String serverKey, SshSession session) {
        // 移除无效连接
    }
}
```

### 9.2 批处理优化

批量操作使用并行流或线程池：

```java
private final ExecutorService executorService = Executors.newFixedThreadPool(10);

public CompletableFuture<Map<String, ExecutionResult>> asyncBatchExecuteCommand(
        List<String> serverIds, String command) {
    List<CompletableFuture<Map.Entry<String, ExecutionResult>>> futures = serverIds.stream()
        .map(serverId -> CompletableFuture.supplyAsync(() -> {
            try {
                return Map.entry(serverId, executeCommand(serverId, command));
            } catch (Exception e) {
                return Map.entry(serverId, new ExecutionResult(-1, "", e.getMessage()));
            }
        }, executorService))
        .collect(Collectors.toList());
    
    return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
        .thenApply(v -> futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
}
```

### 9.3 命令执行优化

减少执行命令次数，合并命令：

```java
public boolean configureFirewall(String serverId, List<Integer> portsToOpen) {
    // 合并多个端口开放为一个命令
    String command = portsToOpen.stream()
        .map(port -> String.format("firewall-cmd --permanent --add-port=%d/tcp", port))
        .collect(Collectors.joining(" && "));
    command += " && firewall-cmd --reload";
    
    ExecutionResult result = serverInfoService.executeCommand(serverId, command);
    return result.isSuccessful();
}
```

## 10. 测试策略

### 10.1 单元测试

- 每个服务和处理器实现单元测试
- 使用 Mockito 模拟依赖
- 使用 MockSSH 库模拟 SSH 服务器

```java
@ExtendWith(MockitoExtension.class)
public class ServerServiceTest {
    @Mock
    private SshService sshService;
    
    @Mock
    private ServerRepository serverRepository;
    
    @InjectMocks
    private ServerServiceImpl serverInfoService;
    
    @Test
    public void testExecuteCommand() {
        // 准备测试数据
        // 设置 Mock 行为
        // 执行测试
        // 验证结果
    }
}
```

### 10.2 集成测试

- 使用 Docker 容器进行集成测试
- 配置测试服务器环境
- 验证完整流程

```java
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
public class ProxyServiceIntegrationTest {
    @Autowired
    private ProxyService proxyService;
    
    @Autowired
    private ServerService serverInfoService;
    
    @Test
    public void testInstallAndStartProxy() {
        // 准备测试环境
        // 执行安装和启动
        // 验证结果
    }
}
```

### 10.3 性能测试

- 高并发条件下测试批量操作
- 测试大规模服务器管理能力
- 测量响应时间和资源消耗

## 11. 部署和运维

### 11.1 容器化部署

- 提供 Docker 镜像
- 支持 Kubernetes 部署

### 11.2 监控与日志

- 使用 Prometheus 监控性能指标
- 使用 ELK 收集和分析日志

```java
@Configuration
public class MetricsConfig {
    @Bean
    public MeterRegistry meterRegistry() {
        // 配置 Prometheus 指标收集
    }
}
```

### 11.3 高可用设计

- 支持水平扩展
- 使用 Redis 实现分布式锁

```java
@Service
public class DistributedLockService {
    private final RedisTemplate<String, String> redisTemplate;
    
    public boolean acquireLock(String lockKey, long timeoutMillis) {
        // 获取分布式锁
    }
    
    public void releaseLock(String lockKey) {
        // 释放分布式锁
    }
}
```

## 12. 版本历史

| 版本号 | 日期 | 更新内容 | 负责人 |
|-------|------|---------|-------|
| 1.0.0 | 2023-05-15 | 初始版本，包含基本服务器管理和SSH功能 | 张三 |
| 1.1.0 | 2023-06-20 | 添加代理软件管理功能 | 李四 |
| 1.2.0 | 2023-08-10 | 添加防火墙和端口转发功能 | 王五 |
| 1.3.0 | 2023-10-05 | 添加基准测试功能 | 赵六 |
| 2.0.0 | 2024-01-15 | 重构架构，提高可扩展性 | 孙七 |
| 2.1.0 | 2024-03-20 | 添加 Hysteria2 支持和性能优化 | 周八 |
| 2.2.0 | 2024-05-10 | 添加安全性增强和连接池功能 | 吴九 |
| 2.3.0 | 2024-07-05 | 添加批量操作和异步处理能力 | 郑十 |

## 13. 参考资料

- [Spring Framework 文档](https://docs.spring.io/spring-framework/reference/)
- [JSch - Java Secure Channel](http://www.jcraft.com/jsch/)
- [Docker API](https://docs.docker.com/engine/api/)
- [Firewalld 文档](https://firewalld.org/documentation/)
- [V2Ray 文档](https://www.v2fly.org/)
- [Shadowsocks 文档](https://shadowsocks.org/guide/what-is-shadowsocks.html)
- [Hysteria2 文档](https://hysteria.network/)
- [SOCAT 文档](http://www.dest-unreach.org/socat/doc/socat.html)
- [HAProxy 文档](http://www.haproxy.org/#docs)
- [Linux 防火墙管理指南](https://www.digitalocean.com/community/tutorials/iptables-essentials-common-firewall-rules-and-commands)

---

_最后更新：2024-08-02_ 