# 多模块结构设计

## 1. 概述

本文档描述了 nanfeng-tools 项目从单体应用重构为多模块结构的设计方案。通过模块化拆分，提高代码的可维护性、可测试性和可重用性，同时支持模块的独立发布和版本管理。

### 1.1 设计目标

- 将功能相关的代码组织到独立的模块中，实现关注点分离
- 减少模块间的耦合，通过明确的接口进行交互
- 支持模块的独立开发、测试和发布
- 提高代码重用性，避免重复实现
- 简化依赖管理，减少依赖冲突

### 1.2 模块划分原则

- **高内聚**：模块内部组件应该紧密相关，共同实现特定功能
- **低耦合**：模块之间的依赖应该最小化，通过接口交互
- **单一职责**：每个模块应该有明确的职责和边界
- **可替换性**：模块的实现应该可以被替换，不影响其他模块
- **合理粒度**：模块既不应过大（难以维护）也不应过小（增加复杂性）

## 2. 模块结构

### 2.1 整体架构

项目被拆分为以下模块：

```
nanfeng-tools/
├── nanfeng-common/           # 通用工具和基础组件模块
├── nanfeng-http/             # HTTP 客户端模块
├── nanfeng-geolocation/      # 地理位置服务模块
├── nanfeng-mailbox/          # 邮箱管理模块
├── nanfeng-notification/     # 通知服务模块
├── nanfeng-outlook/          # Outlook 邮件处理模块
├── nanfeng-vpn/              # VPN 服务管理模块
├── nanfeng-work/             # 工作工具模块
└── nanfeng-app/              # 主应用模块
```

### 2.2 模块依赖关系

![模块依赖关系](../../images/模块依赖关系.png)

模块间的依赖关系如下：

- `nanfeng-common`：基础模块，被所有其他模块依赖
- `nanfeng-http`：依赖 `nanfeng-common`，提供 HTTP 客户端功能
- `nanfeng-geolocation`：依赖 `nanfeng-common` 和 `nanfeng-http`
- `nanfeng-mailbox`：依赖 `nanfeng-common` 和 `nanfeng-http`
- `nanfeng-notification`：依赖 `nanfeng-common` 和 `nanfeng-mailbox`
- `nanfeng-outlook`：依赖 `nanfeng-common`
- `nanfeng-vpn`：依赖 `nanfeng-common`
- `nanfeng-work`：依赖 `nanfeng-common`
- `nanfeng-app`：依赖所有其他模块，作为应用入口

## 3. 模块详细说明

### 3.1 nanfeng-common

**功能**：提供通用工具类、异常处理、基础组件等，被其他模块共同依赖。

**主要组件**：
- 通用工具类（日期、文件、JSON、字符串处理等）
- 异常定义和处理
- 数据库相关基础组件
- Web 相关通用组件

**依赖**：基础库、Spring 核心组件

### 3.2 nanfeng-http

**功能**：封装 HTTP 客户端，提供统一的 API 调用接口。

**主要组件**：
- HTTP 客户端封装
- 请求/响应处理
- 代理配置
- 错误处理

**依赖**：`nanfeng-common`、OkHttp、WebFlux

### 3.3 nanfeng-geolocation

**功能**：提供地理位置相关服务，如 IP 地址查询、地理编码等。

**主要组件**：
- 地理位置 API 集成
- 位置信息处理
- 地理编码/解码

**依赖**：`nanfeng-common`、`nanfeng-http`

### 3.4 nanfeng-mailbox

**功能**：邮箱管理服务，包括创建、配置邮箱等。

**主要组件**：
- 邮箱创建和管理
- 邮件转发规则
- 第三方邮箱服务集成

**依赖**：`nanfeng-common`、`nanfeng-http`、Spring Mail

### 3.5 nanfeng-notification

**功能**：通知服务，支持邮件、事件通知等。

**主要组件**：
- 通知事件定义
- 通知模板
- 通知发送服务

**依赖**：`nanfeng-common`、`nanfeng-mailbox`

### 3.6 nanfeng-outlook

**功能**：Outlook 邮件处理，包括邮件解析、生成等。

**主要组件**：
- Outlook 邮件解析
- 邮件模板处理
- Aspose.Email 集成

**依赖**：`nanfeng-common`、Aspose.Email、邮件处理库

### 3.7 nanfeng-vpn

**功能**：VPN 服务管理，包括服务器管理、代理配置等。

**主要组件**：
- 服务器管理
- SSH 连接和命令执行
- 代理软件管理
- 防火墙管理
- 端口转发

**依赖**：`nanfeng-common`、SSH 客户端库

### 3.8 nanfeng-work

**功能**：工作相关工具，如 Excel 处理、数据转换等。

**主要组件**：
- Excel 数据处理
- EDI 记录处理
- 数据转换工具

**依赖**：`nanfeng-common`、Apache POI、反射工具

### 3.9 nanfeng-app

**功能**：主应用模块，整合所有其他模块，提供应用入口。

**主要组件**：
- 应用配置
- 主程序入口
- 模块集成
- Docker 部署配置

**依赖**：所有其他模块

## 4. 构建和部署

### 4.1 构建系统

项目使用 Gradle 作为构建工具，通过 `settings.gradle` 定义模块结构：

```gradle
rootProject.name = 'nanfeng-tools'

include 'nanfeng-common'
include 'nanfeng-http'
include 'nanfeng-geolocation'
include 'nanfeng-mailbox'
include 'nanfeng-notification'
include 'nanfeng-outlook'
include 'nanfeng-vpn'
include 'nanfeng-work'
include 'nanfeng-app'
```

每个模块有自己的 `build.gradle` 文件，定义模块特定的依赖和配置。

### 4.2 依赖管理

- 根项目 `build.gradle` 定义通用配置和依赖版本
- 子模块通过 `api` 和 `implementation` 声明依赖
- 使用 Spring Boot 依赖管理确保依赖版本一致

### 4.3 部署策略

- 主应用模块 `nanfeng-app` 打包为可执行 JAR
- 提供 Docker 镜像和 docker-compose.yml 配置
- 支持独立部署和集成部署

## 5. 迁移策略

### 5.1 迁移步骤

1. 创建多模块项目结构
2. 定义模块间的依赖关系
3. 将代码按功能划分到相应模块
4. 调整包路径和导入语句
5. 解决循环依赖和其他问题
6. 确保测试覆盖
7. 更新构建和部署脚本

### 5.2 兼容性考虑

- 保持公共 API 不变，确保向后兼容
- 对于不兼容的变更，提供迁移指南
- 使用接口和抽象类减少实现细节暴露

## 6. 未来扩展

- 支持模块的独立版本发布
- 引入更细粒度的模块（如安全模块、监控模块）
- 支持插件化架构，允许动态加载模块
- 考虑微服务架构转型的可能性

---

_最后更新：2024-06-12_ 