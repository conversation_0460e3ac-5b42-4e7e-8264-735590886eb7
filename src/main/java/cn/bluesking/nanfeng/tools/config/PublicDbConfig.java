package cn.bluesking.nanfeng.tools.config;

import cn.bluesking.nanfeng.tools.config.properties.PublicJpaProperties;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import javax.sql.DataSource;
import org.hibernate.cfg.AvailableSettings;
import org.reflections.Reflections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 公共数据库配置类。
 *
 * <p>通过反射自动扫描所有使用 publicTransactionManager 的 @EnableJpaRepositories 注解，
 * 动态收集 basePackages 并配置到 EntityManagerFactory 中。
 *
 * <AUTHOR>
 * @date 2025-06-01
 * @since 2.2.0
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    basePackages = {
        "cn.bluesking.nanfeng.tools.server.server"
    },
    entityManagerFactoryRef = "publicEntityManagerFactory",
    transactionManagerRef = "publicTransactionManager"
)
public class PublicDbConfig {

    private static final Logger logger = LoggerFactory.getLogger(PublicDbConfig.class);

    private final JpaProperties jpaProperties;
    private final HibernateProperties hibernateProperties;
    private final PublicJpaProperties publicJpaProperties;

    /*-------------------- constructor --------------------*/

    public PublicDbConfig(JpaProperties jpaProperties,
                          HibernateProperties hibernateProperties,
                          PublicJpaProperties publicJpaProperties) {

        this.jpaProperties = jpaProperties;
        this.hibernateProperties = hibernateProperties;
        this.publicJpaProperties = publicJpaProperties;
    }

    /*-------------------- bean definition --------------------*/

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.public")
    public DataSource publicDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    public LocalContainerEntityManagerFactoryBean publicEntityManagerFactory() {

        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();

        em.setDataSource(publicDataSource());
        // 通过反射动态收集需要扫描的包
        em.setPackagesToScan(collectPackagesToScan());
        // 配置 JPA 供应商适配器
        em.setJpaVendorAdapter(createVendorAdapter());
        // 从配置文件读取 JPA 属性
        em.setJpaPropertyMap(loadJpaProperties());

        return em;
    }

    @Bean
    public PlatformTransactionManager publicTransactionManager() {

        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(publicEntityManagerFactory().getObject());
        return transactionManager;
    }

    /*-------------------- private method --------------------*/

    /**
     * 通过反射收集所有使用 publicTransactionManager 的 @EnableJpaRepositories 注解的 basePackages。
     *
     * <p>扫描 cn.bluesking 包下所有带有 @EnableJpaRepositories 注解的类，筛选出 transactionManagerRef 为
     * 『publicTransactionManager』的注解，收集这些注解的 basePackages 值并去重。
     *
     * @return 需要扫描的包名数组
     */
    private String[] collectPackagesToScan() {

        Set<String> allPackages = new HashSet<>();
        try {

            // 使用 Reflections 扫描 cn.bluesking 包下的所有类
            Reflections reflections = new Reflections("cn.bluesking");

            // 获取所有带有 @EnableJpaRepositories 注解的类
            Set<Class<?>> annotatedClasses = reflections.getTypesAnnotatedWith(EnableJpaRepositories.class);
            logger.info("found {} classes with @EnableJpaRepositories annotation.", annotatedClasses.size());

            for (Class<?> clazz : annotatedClasses) {

                EnableJpaRepositories annotation = clazz.getAnnotation(EnableJpaRepositories.class);
                if (annotation != null) {
                    // 检查 transactionManagerRef 是否为『publicTransactionManager』
                    if ("publicTransactionManager".equals(annotation.transactionManagerRef())) {

                        // 收集 basePackages
                        String[] basePackages = annotation.basePackages();
                        if (basePackages.length > 0) {
                            allPackages.addAll(Arrays.asList(basePackages));
                            logger.info("collected basePackages from {}: {}", clazz.getSimpleName(),
                                Arrays.toString(basePackages));
                        }
                        else {

                            // 如果没有 basePackages，尝试收集 value 属性。
                            String[] valuePackages = annotation.value();
                            if (valuePackages.length > 0) {
                                allPackages.addAll(Arrays.asList(valuePackages));
                                logger.info("collected value packages from {}: {}", clazz.getSimpleName(),
                                    Arrays.toString(valuePackages));
                            }
                        }
                    }
                    else {
                        logger.debug("skipping class {} with transactionManagerRef: {}", clazz.getSimpleName(),
                            annotation.transactionManagerRef());
                    }
                }
            }

        }
        catch (Exception e) {
            logger.error("failed to collect packages using reflection.", e);
            // 如果反射失败，返回空数组
            return new String[] {};
        }

        String[] result = allPackages.toArray(new String[0]);
        logger.info("final packages to scan: {}", Arrays.toString(result));

        return result;
    }

    /**
     * 创建并配置 Hibernate JPA 供应商适配器。
     *
     * <p>使用 Spring Boot 的配置绑定机制，优先使用 PublicJpaProperties 的配置，如果没有配置则回退到全局的 JpaProperties 配置。
     *
     * @return 配置好的 HibernateJpaVendorAdapter
     */
    private HibernateJpaVendorAdapter createVendorAdapter() {

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();

        // 优先使用 public 特定配置，如果没有则使用全局 JPA 配置。
        boolean generateDdl = publicJpaProperties.getGenerateDdl() != null
            ? publicJpaProperties.getGenerateDdl()
            : jpaProperties.isGenerateDdl();
        vendorAdapter.setGenerateDdl(generateDdl);

        boolean showSql = publicJpaProperties.getShowSql() != null
            ? publicJpaProperties.getShowSql()
            : jpaProperties.isShowSql();
        vendorAdapter.setShowSql(showSql);

        // 数据库平台配置
        String databasePlatform = publicJpaProperties.getDatabasePlatform() != null
            ? publicJpaProperties.getDatabasePlatform()
            : jpaProperties.getDatabasePlatform();
        if (databasePlatform != null && !databasePlatform.trim().isEmpty()) {
            vendorAdapter.setDatabasePlatform(databasePlatform);
        }

        logger.info("configured HibernateJpaVendorAdapter: generateDdl={}, showSql={}, databasePlatform={}",
            generateDdl, showSql, databasePlatform);

        return vendorAdapter;
    }

    /**
     * 从配置文件加载 JPA 属性。
     *
     * <p>使用 Spring Boot 的配置绑定机制，完全避免硬编码：
     * <ul>
     *   <li>首先获取 Spring Boot 的默认 Hibernate 配置。</li>
     *   <li>然后应用 PublicJpaProperties 中的配置覆盖。</li>
     *   <li>最后加载自定义属性扩展。</li>
     * </ul>
     *
     * @return JPA 属性映射
     */
    private HashMap<String, Object> loadJpaProperties() {

        // 使用 Spring Boot 的 HibernateProperties 获取默认配置
        HibernateSettings hibernateSettings = new HibernateSettings();

        Map<String, Object> baseProperties = hibernateProperties.determineHibernateProperties(
            jpaProperties.getProperties(), hibernateSettings);

        HashMap<String, Object> properties = new HashMap<>(baseProperties);

        // 应用 PublicJpaProperties 中的配置
        applyPublicJpaProperties(properties);

        // 加载自定义属性
        properties.putAll(publicJpaProperties.getProperties());

        logger.info("loaded JPA properties: {}", properties);

        return properties;
    }

    /**
     * 应用 PublicJpaProperties 中的 Hibernate 配置。
     *
     * <p>使用 Spring Boot 的配置绑定机制，避免硬编码的属性名称。
     *
     * @param properties 属性映射
     */
    private void applyPublicJpaProperties(HashMap<String, Object> properties) {

        PublicJpaProperties.Hibernate hibernate = publicJpaProperties.getHibernate();

        // DDL 自动生成策略
        if (hibernate.getDdlAuto() != null) {
            properties.put(AvailableSettings.HBM2DDL_AUTO, hibernate.getDdlAuto());
        }

        // 数据库方言
        if (hibernate.getDialect() != null) {
            properties.put(AvailableSettings.DIALECT, hibernate.getDialect());
        }

        // SQL 显示和格式化
        if (hibernate.getShowSql() != null) {
            properties.put(AvailableSettings.SHOW_SQL, hibernate.getShowSql());
        }

        if (hibernate.getFormatSql() != null) {
            properties.put(AvailableSettings.FORMAT_SQL, hibernate.getFormatSql());
        }

        // 命名策略
        PublicJpaProperties.Hibernate.Naming naming = hibernate.getNaming();
        if (naming.getPhysicalStrategy() != null) {
            properties.put(AvailableSettings.PHYSICAL_NAMING_STRATEGY, naming.getPhysicalStrategy());
        }

        if (naming.getImplicitStrategy() != null) {
            properties.put(AvailableSettings.IMPLICIT_NAMING_STRATEGY, naming.getImplicitStrategy());
        }

        // 连接配置
        PublicJpaProperties.Hibernate.Connection connection = hibernate.getConnection();
        if (connection.getPoolSize() != null) {
            properties.put(AvailableSettings.POOL_SIZE, connection.getPoolSize());
        }

        // JDBC 配置
        PublicJpaProperties.Hibernate.Jdbc jdbc = hibernate.getJdbc();
        if (jdbc.getBatchSize() != null) {
            properties.put(AvailableSettings.STATEMENT_BATCH_SIZE, jdbc.getBatchSize());
        }

        if (jdbc.getFetchSize() != null) {
            properties.put(AvailableSettings.STATEMENT_FETCH_SIZE, jdbc.getFetchSize());
        }

        // 缓存配置
        PublicJpaProperties.Hibernate.Cache cache = hibernate.getCache();
        if (cache.getUseSecondLevelCache() != null) {
            properties.put(AvailableSettings.USE_SECOND_LEVEL_CACHE, cache.getUseSecondLevelCache());
        }

        if (cache.getUseQueryCache() != null) {
            properties.put(AvailableSettings.USE_QUERY_CACHE, cache.getUseQueryCache());
        }

        logger.debug("applied PublicJpaProperties configuration");
    }

}