package cn.bluesking.nanfeng.tools.server.server.ssh.service.impl;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Properties;
import java.util.concurrent.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.jcraft.jsch.*;

import cn.bluesking.nanfeng.tools.server.server.ssh.exception.SshConnectionException;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.ExecutionResult;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;
import cn.bluesking.nanfeng.tools.server.server.ssh.service.SshService;

/**
 * SSH服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
@Service
public class SshServiceImpl implements SshService {

    private static final Logger logger = LoggerFactory.getLogger(SshServiceImpl.class);

    private static final int DEFAULT_TIMEOUT = 30000; // 默认超时时间：30秒
    private static final int BUFFER_SIZE = 1024;

    /**
     * 使用密码连接服务器
     *
     * @param host 主机地址
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @return SSH会话
     */
    @Override
    public SshSession connect(String host, int port, String username, String password) {
        logger.info("connecting to {}:{} with username: {}", host, port, username);

        try {
            JSch jsch = new JSch();
            Session session = jsch.getSession(username, host, port);
            session.setPassword(password);

            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.setTimeout(DEFAULT_TIMEOUT);

            logger.debug("connecting to {}:{} with username {}", host, port, username);
            session.connect(DEFAULT_TIMEOUT);
            logger.debug("connected to {}:{}", host, port);

            return new SshSession(session, host, port, username);
        }

        catch (JSchException e) {
            logger.error(
                "failed to connect to {}:{} with username {}: {}",
                host,
                port,
                username,
                e.getMessage());
            throw new SshConnectionException(host, e);
        }

    }

    /**
     * 使用密钥连接服务器
     *
     * @param host 主机地址
     * @param port 端口
     * @param username 用户名
     * @param privateKey 私钥内容
     * @return SSH会话
     */
    @Override
    public SshSession connectWithKey(String host, int port, String username, String privateKey) {
        logger.info(
            "connecting to {}:{} with username: {} using private key", host, port, username);

        try {
            JSch jsch = new JSch();

            // 添加私钥
            byte[] privateKeyBytes = privateKey.getBytes(StandardCharsets.UTF_8);
            jsch.addIdentity(username, privateKeyBytes, null, null);

            Session session = jsch.getSession(username, host, port);

            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.setTimeout(DEFAULT_TIMEOUT);

            logger.debug(
                "connecting to {}:{} with username {} using private key", host, port, username);
            session.connect(DEFAULT_TIMEOUT);
            logger.debug("connected to {}:{} using private key", host, port);

            return new SshSession(session, host, port, username);
        }

        catch (JSchException e) {
            logger.error(
                "failed to connect to {}:{} with username {} using private key: {}",
                host,
                port,
                username,
                e.getMessage());
            throw new SshConnectionException(host, e);
        }

    }

    /**
     * 执行命令
     *
     * @param session SSH会话
     * @param command 命令
     * @return 执行结果
     */
    @Override
    public ExecutionResult executeCommand(SshSession session, String command) {
        return executeCommandWithTimeout(session, command, DEFAULT_TIMEOUT);
    }

    /**
     * 带超时执行命令
     *
     * @param session SSH会话
     * @param command 命令
     * @param timeout 超时时间（毫秒）
     * @return 执行结果
     */
    @Override
    public ExecutionResult executeCommandWithTimeout(
        SshSession session, String command, long timeout) {
        logger.debug(
            "executing command on {}:{}: {}", session.getHost(), session.getPort(), command);

        if (session == null || !isConnected(session)) {
            logger.error("ssh session is not connected");
            return new ExecutionResult(-1, "", "SSH session is not connected");
        }

        Session jschSession = (Session) session.getSession();
        ChannelExec channel = null;
        long startTime = System.currentTimeMillis();
        boolean timedOut = false;

        try {
            channel = (ChannelExec) jschSession.openChannel("exec");
            channel.setCommand(command);

            ByteArrayOutputStream stdout = new ByteArrayOutputStream();
            ByteArrayOutputStream stderr = new ByteArrayOutputStream();
            channel.setOutputStream(stdout);
            channel.setErrStream(stderr);

            logger.debug("executing command: {}", command);
            channel.connect(DEFAULT_TIMEOUT);

            // 使用 Future 实现超时控制
            ExecutorService executor = Executors.newSingleThreadExecutor();
            final ChannelExec finalChannel = channel;
            Future<Integer> future =
                executor.submit(
                    () -> {
                        while (!finalChannel.isClosed()) {
                            try {
                                Thread.sleep(100);
                            }

                            catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                break;
                            }

                        }

                        return finalChannel.getExitStatus();
                    }

                );

            int exitCode;
            try {
                exitCode = future.get(timeout, TimeUnit.MILLISECONDS);
            }

            catch (TimeoutException e) {
                future.cancel(true);
                timedOut = true;
                exitCode = -1;
                logger.warn("command execution timed out after {} ms: {}", timeout, command);
            }

            catch (Exception e) {
                future.cancel(true);
                exitCode = -1;
                logger.error("error waiting for command completion: {}", e.getMessage());
            }

            executor.shutdownNow();

            long executionTime = System.currentTimeMillis() - startTime;
            String stdoutStr = stdout.toString(StandardCharsets.UTF_8);
            String stderrStr = stderr.toString(StandardCharsets.UTF_8);

            logger.debug(
                "command completed with exit code {}, took {} ms", exitCode, executionTime);

            // 更新会话最后活动时间
            session.updateLastActiveTime();

            ExecutionResult result =
                new ExecutionResult(exitCode, stdoutStr, stderrStr, executionTime, timedOut);

            if (result.isSuccessful()) {
                logger.debug(
                    "command executed successfully on {}:{}, time: {}ms",
                    session.getHost(),
                    session.getPort(),
                    executionTime);
            }

            else {
                logger.warn(
                    "command execution failed on {}:{}, exit code: {}, time: {}ms",
                    session.getHost(),
                    session.getPort(),
                    exitCode,
                    executionTime);
            }

            return result;
        }

        catch (JSchException e) {
            logger.error("failed to execute command: {}", e.getMessage());
            return new ExecutionResult(-1, "", e.getMessage());
        }

        finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }

        }

    }

    /**
     * 上传文件
     *
     * @param session SSH会话
     * @param localPath 本地路径
     * @param remotePath 远程路径
     * @return 是否成功
     */
    @Override
    public boolean uploadFile(SshSession session, String localPath, String remotePath) {
        logger.info("uploading file from {} to {}:{}", localPath, session.getHost(), remotePath);

        if (session == null || !isConnected(session)) {
            logger.error("ssh session is not connected");
            return false;
        }

        Session jschSession = (Session) session.getSession();
        ChannelSftp channel = null;

        try {
            channel = (ChannelSftp) jschSession.openChannel("sftp");
            channel.connect(DEFAULT_TIMEOUT);

            logger.debug("uploading file from {} to {}", localPath, remotePath);

            // 确保目标目录存在
            String remoteDir = remotePath.substring(0, remotePath.lastIndexOf('/'));
            try {
                createRemoteDirectories(channel, remoteDir);
            }

            catch (SftpException e) {
                logger.warn("failed to create remote directory {}: {}", remoteDir, e.getMessage());
            }

            // 上传文件
            try (InputStream in = new FileInputStream(localPath)) {
                channel.put(in, remotePath);
            }

            logger.debug("file uploaded successfully");

            // 更新会话最后活动时间
            session.updateLastActiveTime();

            return true;
        }

        catch (JSchException | IOException | SftpException e) {
            logger.error("failed to upload file: {}", e.getMessage());
            return false;
        }

        finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }

        }

    }

    /**
     * 下载文件
     *
     * @param session SSH会话
     * @param remotePath 远程路径
     * @param localPath 本地路径
     * @return 是否成功
     */
    @Override
    public boolean downloadFile(SshSession session, String remotePath, String localPath) {
        logger.info("downloading file from {}:{} to {}", session.getHost(), remotePath, localPath);

        if (session == null || !isConnected(session)) {
            logger.error("ssh session is not connected");
            return false;
        }

        Session jschSession = (Session) session.getSession();
        ChannelSftp channel = null;

        try {
            channel = (ChannelSftp) jschSession.openChannel("sftp");
            channel.connect(DEFAULT_TIMEOUT);

            logger.debug("downloading file from {} to {}", remotePath, localPath);

            // 确保本地目录存在
            File localFile = new File(localPath);
            File localDir = localFile.getParentFile();
            if (localDir != null && !localDir.exists()) {
                localDir.mkdirs();
            }

            // 下载文件
            channel.get(remotePath, localPath);

            logger.debug("file downloaded successfully");

            // 更新会话最后活动时间
            session.updateLastActiveTime();

            return true;
        }

        catch (JSchException | SftpException e) {
            logger.error("failed to download file: {}", e.getMessage());
            return false;
        }

        finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }

        }

    }

    /**
     * 上传文本内容
     *
     * @param session SSH会话
     * @param content 文本内容
     * @param remotePath 远程路径
     * @return 是否成功
     */
    @Override
    public boolean uploadText(SshSession session, String content, String remotePath) {
        logger.info("uploading text content to {}:{}", session.getHost(), remotePath);

        if (session == null || !isConnected(session)) {
            logger.error("ssh session is not connected");
            return false;
        }

        Session jschSession = (Session) session.getSession();
        ChannelSftp channel = null;

        try {
            channel = (ChannelSftp) jschSession.openChannel("sftp");
            channel.connect(DEFAULT_TIMEOUT);

            logger.debug("uploading text content to {}", remotePath);

            // 确保目标目录存在
            String remoteDir = remotePath.substring(0, remotePath.lastIndexOf('/'));
            try {
                createRemoteDirectories(channel, remoteDir);
            }

            catch (SftpException e) {
                logger.warn("failed to create remote directory {}: {}", remoteDir, e.getMessage());
            }

            // 上传文本内容
            try (InputStream in =
                     new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8))) {
                channel.put(in, remotePath);
            }

            logger.debug("text content uploaded successfully");

            // 更新会话最后活动时间
            session.updateLastActiveTime();

            return true;
        }

        catch (JSchException | IOException | SftpException e) {
            logger.error("failed to upload text content: {}", e.getMessage());
            return false;
        }

        finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }

        }

    }

    /**
     * 下载文本内容
     *
     * @param session SSH会话
     * @param remotePath 远程路径
     * @return 文本内容
     */
    @Override
    public String downloadText(SshSession session, String remotePath) {
        logger.info("downloading text content from {}:{}", session.getHost(), remotePath);

        if (session == null || !isConnected(session)) {
            logger.error("ssh session is not connected");
            return null;
        }

        Session jschSession = (Session) session.getSession();
        ChannelSftp channel = null;

        try {
            channel = (ChannelSftp) jschSession.openChannel("sftp");
            channel.connect(DEFAULT_TIMEOUT);

            logger.debug("downloading text content from {}", remotePath);

            // 下载文本内容
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            channel.get(remotePath, outputStream);
            String content = outputStream.toString(StandardCharsets.UTF_8);

            logger.debug("text content downloaded successfully");

            // 更新会话最后活动时间
            session.updateLastActiveTime();

            return content;
        }

        catch (JSchException | SftpException e) {
            logger.error("failed to download text content: {}", e.getMessage());
            return null;
        }

        finally {
            if (channel != null && channel.isConnected()) {
                channel.disconnect();
            }

        }

    }

    /**
     * 断开连接
     *
     * @param session SSH会话
     */
    @Override
    public void disconnect(SshSession session) {
        if (session != null && session.getSession() instanceof Session) {
            Session jschSession = (Session) session.getSession();
            if (jschSession.isConnected()) {
                logger.debug("disconnecting from {}:{}", session.getHost(), session.getPort());
                jschSession.disconnect();
                session.setConnected(false);
            }

        }

    }

    /**
     * 检查是否已连接
     *
     * @param session SSH会话
     * @return 是否已连接
     */
    @Override
    public boolean isConnected(SshSession session) {
        if (session == null || !(session.getSession() instanceof Session)) {
            return false;
        }

        Session jschSession = (Session) session.getSession();
        return jschSession.isConnected();
    }

    /**
     * 创建远程目录（递归）
     *
     * @param channel SFTP通道
     * @param dirPath 目录路径
     * @throws SftpException SFTP异常
     */
    private void createRemoteDirectories(ChannelSftp channel, String dirPath) throws SftpException {
        if (dirPath.equals("")) {
            return;
        }

        try {
            channel.cd(dirPath);
            return;
        }

        catch (SftpException e) {
            // 目录不存在，需要创建
        }

        int lastSlashIndex = dirPath.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            // 根目录
            channel.mkdir(dirPath);
            channel.cd(dirPath);
            return;
        }

        // 递归创建父目录
        String parentDir = dirPath.substring(0, lastSlashIndex);
        String childDir = dirPath.substring(lastSlashIndex + 1);

        if (parentDir.isEmpty()) {
            parentDir = "/";
        }

        createRemoteDirectories(channel, parentDir);

        try {
            channel.mkdir(childDir);
        }

        catch (SftpException e) {
            // 目录可能已存在
            if (e.id != ChannelSftp.SSH_FX_FAILURE) {
                throw e;
            }

        }

        channel.cd(childDir);
    }

}