package cn.bluesking.nanfeng.tools.server.server.ssh.exception;

import cn.bluesking.nanfeng.tools.server.server.base.exception.ServerModuleException;

/**
 * SSH连接异常
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public class SshConnectionException extends ServerModuleException {

    /**
     * 构造函数
     *
     * @param host 主机地址
     * @param cause 原因
     */
    public SshConnectionException(String host, Throwable cause) {
        super("SSH连接失败: " + host, cause);
    }

}