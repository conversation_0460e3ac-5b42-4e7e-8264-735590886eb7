package cn.bluesking.nanfeng.tools.server.server.base.exception;

/**
 * 服务器模块异常基类
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public class ServerModuleException extends RuntimeException {

    /** 错误码 */
    private final int code;

    /*-------------------- constructor --------------------*/

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public ServerModuleException(String message) {
        super(message);
        this.code = 500;
    }

/**
     * 构造函数
     *
     * @param code 错误码
     * @param message 错误信息
     */
    public ServerModuleException(int code, String message) {
        super(message);
        this.code = code;
    }

/**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause 原因
     */
    public ServerModuleException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
    }

/**
     * 构造函数
     *
     * @param code 错误码
     * @param message 错误信息
     * @param cause 原因
     */
    public ServerModuleException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    /*-------------------- getter --------------------*/

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    public int getCode() {
        return code;
    }

}