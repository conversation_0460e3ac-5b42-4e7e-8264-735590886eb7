package cn.bluesking.nanfeng.tools.server.server.proxy.model.config;

import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.Map;

/**
 * Shadowsocks 配置类
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public class ShadowsocksConfig extends ProxyConfig {

    /** 密码 */
    private String password;

    /** 加密方法 */
    private String method;

    /** 是否启用UDP */
    private boolean enableUdp;

    /** 是否启用IPv6 */
    private boolean enableIpv6;

    /** 是否启用TCP快速打开 */
    private boolean fastOpen;

    /** 是否启用重用端口 */
    private boolean reusePort;

    /** 是否启用无连接 */
    private boolean noDelay;

    /** 服务器地址 */
    private String serverAddress;

    /** 超时时间（秒） */
    private int timeout;

    /** 插件 */
    private String plugin;

    /** 插件选项 */
    private String pluginOpts;

    /*-------------------- constructor --------------------*/

    /** 默认构造函数 */
    public ShadowsocksConfig() {
        super();
        this.method = "aes-256-gcm";
        this.enableUdp = true;
        this.enableIpv6 = false;
        this.fastOpen = true;
        this.reusePort = true;
        this.noDelay = true;
        this.timeout = 300;
    }

/**
     * 带参数构造函数
     *
     * @param id 代理ID
     * @param port 代理端口
     * @param password 密码
     * @param method 加密方法
     */
    public ShadowsocksConfig(String id, int port, String password, String method) {
        super(id, ProxyType.SHADOWSOCKS, port);
        this.password = password;
        this.method = method;
        this.enableUdp = true;
        this.enableIpv6 = false;
        this.fastOpen = true;
        this.reusePort = true;
        this.noDelay = true;
        this.timeout = 300;
    }

/*-------------------- public method --------------------*/

    /**
     * 获取代理类型
     *
     * @return 代理类型
     */
    @Override
    @JsonIgnore
    public ProxyType getType() {
        return ProxyType.SHADOWSOCKS;
    }

/**
     * 转换为配置字符串
     *
     * @return 配置字符串
     */
    @Override
    @JsonIgnore
    public String toConfigString() {
        Map<String, Object> config = new HashMap<>();

        config.put("server", "0.0.0.0");
        config.put("server_port", getPort());
        config.put("password", password);
        config.put("method", method);
        config.put("timeout", timeout);
        config.put("fast_open", fastOpen);
        config.put("reuse_port", reusePort);
        config.put("no_delay", noDelay);
        config.put("udp", enableUdp);
        config.put("ipv6", enableIpv6);

        if (plugin != null && !plugin.isEmpty()) {
            config.put("plugin", plugin);
            if (pluginOpts != null && !pluginOpts.isEmpty()) {
                config.put("plugin_opts", pluginOpts);
            }

}

try {
            return new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(config);
        }

catch (JsonProcessingException e) {
            return "{}";
        }

}

/**
     * 生成客户端分享链接
     *
     * @param serverAddress 服务器地址
     * @return 分享链接
     */
    public String generateShareLink(String serverAddress) {
        StringBuilder link = new StringBuilder("ss://");

        String userInfo = method + ":" + password;
        String base64UserInfo = java.util.Base64.getEncoder().encodeToString(userInfo.getBytes());

        link.append(base64UserInfo).append("@").append(serverAddress).append(":").append(getPort());

        if (plugin != null && !plugin.isEmpty()) {
            try {
                link.append("/?plugin=").append(java.net.URLEncoder.encode(plugin, "UTF-8"));

                if (pluginOpts != null && !pluginOpts.isEmpty()) {
                    link.append(";").append(java.net.URLEncoder.encode(pluginOpts, "UTF-8"));
                }

}

catch (java.io.UnsupportedEncodingException e) {
                // 忽略编码异常
            }

}

if (getRemark() != null && !getRemark().isEmpty()) {
            try {
                link.append("#").append(java.net.URLEncoder.encode(getRemark(), "UTF-8"));
            }

catch (java.io.UnsupportedEncodingException e) {
                // 忽略编码异常
            }

}

return link.toString();
    }

/*-------------------- getter --------------------*/

    public String getPassword() {
        return password;
    }

public String getMethod() {
        return method;
    }

public boolean isEnableUdp() {
        return enableUdp;
    }

public boolean isEnableIpv6() {
        return enableIpv6;
    }

public boolean isFastOpen() {
        return fastOpen;
    }

public boolean isReusePort() {
        return reusePort;
    }

public boolean isNoDelay() {
        return noDelay;
    }

public String getServerAddress() {
        return serverAddress;
    }

public int getTimeout() {
        return timeout;
    }

public String getPlugin() {
        return plugin;
    }

public String getPluginOpts() {
        return pluginOpts;
    }

/*-------------------- setter --------------------*/

    public void setPassword(String password) {
        this.password = password;
    }

public void setMethod(String method) {
        this.method = method;
    }

public void setEnableUdp(boolean enableUdp) {
        this.enableUdp = enableUdp;
    }

public void setEnableIpv6(boolean enableIpv6) {
        this.enableIpv6 = enableIpv6;
    }

public void setFastOpen(boolean fastOpen) {
        this.fastOpen = fastOpen;
    }

public void setReusePort(boolean reusePort) {
        this.reusePort = reusePort;
    }

public void setNoDelay(boolean noDelay) {
        this.noDelay = noDelay;
    }

public void setServerAddress(String serverAddress) {
        this.serverAddress = serverAddress;
    }

public void setTimeout(int timeout) {
        this.timeout = timeout;
    }

public void setPlugin(String plugin) {
        this.plugin = plugin;
    }

public void setPluginOpts(String pluginOpts) {
        this.pluginOpts = pluginOpts;
    }

}