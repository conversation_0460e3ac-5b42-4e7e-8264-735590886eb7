package cn.bluesking.nanfeng.tools.server.server.firewall.exception;

import cn.bluesking.nanfeng.tools.server.server.base.exception.ServerModuleException;

/**
 * 防火墙操作异常
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public class FirewallOperationException extends ServerModuleException {

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public FirewallOperationException(String message) {
        super(message);
    }

/**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause 原因
     */
    public FirewallOperationException(String message, Throwable cause) {
        super(message, cause);
    }

}