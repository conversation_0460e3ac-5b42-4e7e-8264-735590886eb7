package cn.bluesking.nanfeng.tools.server.server.proxy.model.config;

import cn.bluesking.nanfeng.tools.common.utils.JsonUtils;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyType;
import com.google.common.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 代理配置工厂类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
public class ProxyConfigFactory {

private static final Logger logger = LoggerFactory.getLogger(ProxyConfigFactory.class);

    // 类型映射，将代理类型映射到对应的配置类
    private static final Map<ProxyType, Class<? extends ProxyConfig>> TYPE_MAP = new HashMap<>();

    // 初始化类型映射
    static {
        TYPE_MAP.put(ProxyType.V2RAY, V2RayConfig.class);
        TYPE_MAP.put(ProxyType.SHADOWSOCKS, ShadowsocksConfig.class);
        // 如果有其他类型，可以在这里添加
}

    /**
     * 创建指定类型的代理配置实例
     *
     * @param type 代理类型
     * @return 代理配置实例
     */
    public static ProxyConfig createConfig(ProxyType type) {
        switch (type) {
            case V2RAY:
                return new V2RayConfig();
            case SHADOWSOCKS:
                return new ShadowsocksConfig();
            // 添加其他类型的处理
            default:
                throw new IllegalArgumentException("不支持的代理类型: " + type);

}
    }

    private static final Type JSON_MAP_TYPE = new TypeToken<Map<String, Object>>() {}.getType();

    /**
     * 从JSON字符串解析代理配置
     *
     * @param json JSON字符串
     * @return 代理配置实例，解析失败返回null。
     */
    public static ProxyConfig fromJson(String json) {
        try {
            // 首先解析 JSON 字符串到 Map，获取 type 字段
            Map<String, Object> jsonMap = JsonUtils.fromJson(json, JSON_MAP_TYPE);
            if (jsonMap == null) {
                logger.error("解析JSON为Map失败");
                return null;

}

            // 从Map中获取type字段
            String typeStr = (String) jsonMap.get("type");
            if (typeStr == null) {
                logger.error("JSON中缺少type字段");
                return null;

}

            // 根据type字符串查找对应的ProxyType枚举值
            ProxyType type = null;
            if ("v2ray".equalsIgnoreCase(typeStr)) {
                type = ProxyType.V2RAY;

}

else if ("shadowsocks".equalsIgnoreCase(typeStr)) {
                type = ProxyType.SHADOWSOCKS;

}

else {
                logger.error("不支持的代理类型: {}", typeStr);
                return null;

}

            // 根据类型创建具体的配置实例
            ProxyConfig config = createConfig(type);

            // 将JSON Map解析到配置实例中
            parseJsonMapToConfig(jsonMap, config);

            return config;

}

catch (Exception e) {
            logger.error("解析JSON字符串失败: {}", e.getMessage(), e);
            return null;

}
    }

    /**
     * 将JSON Map解析到配置实例中
     *
     * @param jsonMap JSON Map
     * @param config 配置实例
     */
    private static void parseJsonMapToConfig(Map<String, Object> jsonMap, ProxyConfig config) {
        // 解析基本字段
        parseBasicFields(jsonMap, config);

        // 根据配置类型，解析特定字段
        if (config instanceof V2RayConfig) {
            parseV2RayConfig(jsonMap, (V2RayConfig) config);

}

else if (config instanceof ShadowsocksConfig) {
            parseShadowsocksConfig(jsonMap, (ShadowsocksConfig) config);

}
        // 添加其他类型的解析
    }

    /**
     * 解析基本字段
     *
     * @param jsonMap JSON Map
     * @param config 配置实例
     */
    private static void parseBasicFields(Map<String, Object> jsonMap, ProxyConfig config) {
        // 解析id字段
        setIfNotNull(jsonMap, "id", config::setId);

        // 解析server字段
        setIfNotNull(jsonMap, "server", config::setServer);

        // 解析port字段
        if (jsonMap.containsKey("port")) {
            try {
                Object portObj = jsonMap.get("port");
                if (portObj instanceof Number) {
                    config.setPort(((Number) portObj).intValue());

}

else if (portObj instanceof String) {
                    config.setPort(Integer.parseInt((String) portObj));

}
            }

catch (NumberFormatException e) {
                logger.warn("解析port字段失败: {}", e.getMessage());

}
        }

        // 解析remark字段
        setIfNotNull(jsonMap, "remark", config::setRemark);

        // 解析autoStart字段
        if (jsonMap.containsKey("autoStart")) {
            Object autoStartObj = jsonMap.get("autoStart");
            if (autoStartObj instanceof Boolean) {
                config.setAutoStart((Boolean) autoStartObj);

}

else if (autoStartObj instanceof String) {
                config.setAutoStart(Boolean.parseBoolean((String) autoStartObj));

}
        }

}

    /**
     * 解析V2Ray配置特有字段
     *
     * @param jsonMap JSON Map
     * @param config V2Ray配置实例
     */
    private static void parseV2RayConfig(Map<String, Object> jsonMap, V2RayConfig config) {
        setIfNotNull(jsonMap, "protocol", config::setProtocol);
        setIfNotNull(jsonMap, "uuid", config::setUuid);
        setIfNotNull(jsonMap, "security", config::setSecurity);
        setIfNotNull(jsonMap, "network", config::setNetwork);
        setIfNotNull(jsonMap, "headerType", config::setHeaderType);
        setIfNotNull(jsonMap, "host", config::setHost);
        setIfNotNull(jsonMap, "path", config::setPath);

        if (jsonMap.containsKey("enableTls")) {
            Object enableTlsObj = jsonMap.get("enableTls");
            if (enableTlsObj instanceof Boolean) {
                config.setEnableTls((Boolean) enableTlsObj);

}

else if (enableTlsObj instanceof String) {
                config.setEnableTls(Boolean.parseBoolean((String) enableTlsObj));

}
        }

        setIfNotNull(jsonMap, "tlsServerName", config::setTlsServerName);
}

    /**
     * 解析Shadowsocks配置特有字段
     *
     * @param jsonMap JSON Map
     * @param config Shadowsocks配置实例
     */
    private static void parseShadowsocksConfig(Map<String, Object> jsonMap, ShadowsocksConfig config) {
        setIfNotNull(jsonMap, "password", config::setPassword);
        setIfNotNull(jsonMap, "method", config::setMethod);
        setBooleanIfExists(jsonMap, "enableUdp", config::setEnableUdp);
        setBooleanIfExists(jsonMap, "enableIpv6", config::setEnableIpv6);
        setBooleanIfExists(jsonMap, "fastOpen", config::setFastOpen);
        setBooleanIfExists(jsonMap, "reusePort", config::setReusePort);
        setBooleanIfExists(jsonMap, "noDelay", config::setNoDelay);
        setIfNotNull(jsonMap, "serverAddress", config::setServerAddress);

        if (jsonMap.containsKey("timeout")) {
            try {
                Object timeoutObj = jsonMap.get("timeout");
                if (timeoutObj instanceof Number) {
                    config.setTimeout(((Number) timeoutObj).intValue());

}

else if (timeoutObj instanceof String) {
                    config.setTimeout(Integer.parseInt((String) timeoutObj));

}
            }

catch (NumberFormatException e) {
                logger.warn("解析timeout字段失败: {}", e.getMessage());

}
        }

        setIfNotNull(jsonMap, "plugin", config::setPlugin);
        setIfNotNull(jsonMap, "pluginOpts", config::setPluginOpts);
}

    /**
     * 如果字段存在且不为null，则设置字符串值。
     *
     * @param jsonMap JSON Map
     * @param key 字段名
     * @param setter 字段设置器
     */
    private static void setIfNotNull(Map<String, Object> jsonMap, String key, StringSetter setter) {
        if (jsonMap.containsKey(key) && jsonMap.get(key) != null) {
            setter.setValue(jsonMap.get(key).toString());

}
    }

    /**
     * 如果字段存在，则设置布尔值。
     *
     * @param jsonMap JSON Map
     * @param key 字段名
     * @param setter 字段设置器
     */
    private static void setBooleanIfExists(Map<String, Object> jsonMap, String key, BooleanSetter setter) {
        if (jsonMap.containsKey(key)) {
            Object boolObj = jsonMap.get(key);
            if (boolObj instanceof Boolean) {
                setter.setValue((Boolean) boolObj);

}

else if (boolObj instanceof String) {
                setter.setValue(Boolean.parseBoolean((String) boolObj));

}
        }

}

    /**
     * 字符串字段设置器接口
     */
    @FunctionalInterface
    private interface StringSetter {
        void setValue(String value);
}

    /**
     * 整数字段设置器接口
     */
    @FunctionalInterface
    private interface IntSetter {
        void setValue(int value);
}

    /**
     * 布尔字段设置器接口
     */
    @FunctionalInterface
    private interface BooleanSetter {
        void setValue(boolean value);

}

}