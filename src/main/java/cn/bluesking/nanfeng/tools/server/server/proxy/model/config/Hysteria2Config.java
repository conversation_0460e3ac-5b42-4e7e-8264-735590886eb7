package cn.bluesking.nanfeng.tools.server.server.proxy.model.config;

import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyType;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;

/**
 * Hysteria2 代理配置类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
public class Hysteria2Config extends ProxyConfig {

    private int uploadMbps;
    private int downloadMbps;
    private String password;
    private String obfs;
    private String obfsPassword;
    private boolean ignoreClientBandwidth;
    private ProxyType type;
    private boolean enableTls;

    /**
     * 默认构造函数
     */
    public Hysteria2Config() {
        super();
        this.setType(ProxyType.HYSTERIA2);
        this.uploadMbps = 100;
        this.downloadMbps = 100;
        this.ignoreClientBandwidth = true;
        this.setEnableTls(true); // Hysteria2 必须启用 TLS
    }

    /**
     * 构造函数
     *
     * @param id 代理ID
     * @param port 端口
     * @param password 密码
     */
    public Hysteria2Config(String id, int port, String password) {
        super(id, ProxyType.HYSTERIA2, port);
        this.password = password;
        this.uploadMbps = 100;
        this.downloadMbps = 100;
        this.ignoreClientBandwidth = true;
        this.setEnableTls(true); // Hysteria2 必须启用 TLS
    }

    /**
     * 构造函数
     *
     * @param id 代理ID
     * @param port 端口
     * @param password 密码
     * @param uploadMbps 上传带宽(Mbps)
     * @param downloadMbps 下载带宽(Mbps)
     */
    public Hysteria2Config(String id, int port, String password, int uploadMbps, int downloadMbps) {
        super(id, ProxyType.HYSTERIA2, port);
        this.password = password;
        this.uploadMbps = uploadMbps;
        this.downloadMbps = downloadMbps;
        this.ignoreClientBandwidth = true;
        this.setEnableTls(true); // Hysteria2 必须启用 TLS
    }

    @Override
    public String toConfigString() {
        JsonObject config = new JsonObject();

        // 监听配置
        config.addProperty("listen", ":" + getPort());

        // 身份验证
        if (password != null && !password.isEmpty()) {
            config.addProperty("auth", password);
        }

        // 带宽配置
        if (!ignoreClientBandwidth) {
            JsonObject bandwidth = new JsonObject();
            bandwidth.addProperty("up", uploadMbps + " mbps");
            bandwidth.addProperty("down", downloadMbps + " mbps");
            config.add("bandwidth", bandwidth);
        }

        // 混淆配置
        if (obfs != null && !obfs.isEmpty()) {
            JsonObject obfsConfig = new JsonObject();
            obfsConfig.addProperty("type", obfs);
            if (obfsPassword != null && !obfsPassword.isEmpty()) {
                obfsConfig.addProperty("password", obfsPassword);
            }

            config.add("obfs", obfsConfig);
        }

        // 速率配置
        JsonObject masquerade = new JsonObject();
        masquerade.addProperty("type", "proxy");
        masquerade.addProperty("proxy", "http://mirror.nl.leaseweb.net/speedtest/10mb.bin");
            config.add("masquerade", masquerade);

                // 转换为格式化YAML字符串
                Gson gson = new GsonBuilder().setPrettyPrinting().create();
        return gson.toJson(config);
    }

    /**
     * 生成客户端命令行连接字符串
     *
     * @param serverAddress 服务器地址
     * @return 连接字符串
     */
    public String generateClientCommand(String serverAddress) {
        StringBuilder command = new StringBuilder("./hysteria2 -c '");

        command.append("base: ").append(serverAddress).append(":").append(getPort()).append("\n");
        command.append("auth: ").append(password).append("\n");

        if (obfs != null && !obfs.isEmpty()) {
            command.append("obfs: ").append(obfs).append("\n");

            if (obfsPassword != null && !obfsPassword.isEmpty()) {
                command.append("obfs-password: ").append(obfsPassword).append("\n");
            }

        }

        command.append("bandwidth: \n");
        command.append("  up: ").append(uploadMbps).append(" mbps\n");
        command.append("  down: ").append(downloadMbps).append(" mbps\n");

        command.append("tls: \n");
        command.append("  sni: ").append(serverAddress).append("\n");
        command.append("  insecure: true").append("\n");

        command.append("socks5: \n");
        command.append("  listen: 127.0.0.1:1080").append("\n");

        command.append("'");

        return command.toString();
    }

    /*-------------------- getter & setter --------------------*/

    public int getUploadMbps() {
        return uploadMbps;
    }

    public void setUploadMbps(int uploadMbps) {
        this.uploadMbps = uploadMbps;
    }

    public int getDownloadMbps() {
        return downloadMbps;
    }

    public void setDownloadMbps(int downloadMbps) {
        this.downloadMbps = downloadMbps;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getObfs() {
        return obfs;
    }

    public void setObfs(String obfs) {
        this.obfs = obfs;
    }

    public String getObfsPassword() {
        return obfsPassword;
    }

    public void setObfsPassword(String obfsPassword) {
        this.obfsPassword = obfsPassword;
    }

    public boolean isIgnoreClientBandwidth() {
        return ignoreClientBandwidth;
    }

    public void setIgnoreClientBandwidth(boolean ignoreClientBandwidth) {
        this.ignoreClientBandwidth = ignoreClientBandwidth;
    }

    @Override
    public ProxyType getType() {
        return type;
    }

    public void setType(ProxyType type) {
        this.type = type;
    }

    public boolean isEnableTls() {
        return enableTls;
    }

    public void setEnableTls(boolean enableTls) {
        this.enableTls = enableTls;
    }

}