package cn.bluesking.nanfeng.tools.server.server.proxy.infrastructure.impl;

import cn.bluesking.nanfeng.tools.server.server.proxy.model.config.V2RayConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.bluesking.nanfeng.tools.server.server.proxy.infrastructure.ProxyHandler;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyStatus;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyType;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.config.ProxyConfig;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.ExecutionResult;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;
import cn.bluesking.nanfeng.tools.server.server.ssh.service.SshService;

/**
 * V2Ray代理处理器实现类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
@Component
public class V2Ray<PERSON><PERSON><PERSON> implements ProxyHandler {

    private static final Logger logger = LoggerFactory.getLogger(V2RayHandler.class);
    private static final String INSTALL_SCRIPT =
        "bash <(curl -L"
            + " https://raw.githubusercontent.com/v2fly/fhs-install-v2ray/master/install-release.sh)";
    private static final String CONFIG_PATH = "/usr/local/etc/v2ray/config.json";
    private static final String SERVICE_NAME = "v2ray";

    @Autowired
    private SshService sshService;

    @Override
    public ProxyType getType() {
        return ProxyType.V2RAY;
    }

    @Override
    public boolean install(SshSession session, ProxyConfig config) {
        if (!(config instanceof V2RayConfig)) {
            logger.error("config must be instance of V2RayConfig");
            return false;
        }

        logger.info("installing v2ray...");

        // 1. 安装V2Ray
        ExecutionResult result = sshService.executeCommand(session, INSTALL_SCRIPT);
        if (!result.isSuccessful()) {
            logger.error("failed to install v2ray: {}", result.getStderr());
            return false;
        }

        // 2. 配置V2Ray
        if (!configure(session, config)) {
            logger.error("failed to configure v2ray");
            return false;
        }

        // 3. 启动V2Ray
        if (!start(session)) {
            logger.error("failed to start v2ray");
            return false;
        }

        logger.info("v2ray installed and started successfully");
        return true;
    }

    @Override
    public boolean update(SshSession session) {
        logger.info("updating v2ray...");

        // 执行更新脚本
        ExecutionResult result = sshService.executeCommand(session, INSTALL_SCRIPT);
        if (!result.isSuccessful()) {
            logger.error("failed to update v2ray: {}", result.getStderr());
            return false;
        }

        // 重启服务
        if (getStatus(session) == ProxyStatus.RUNNING) {
            if (!restart(session)) {
                logger.error("failed to restart v2ray after update");
                return false;
            }

        }

        logger.info("v2ray updated successfully");
        return true;
    }

    @Override
    public boolean configure(SshSession session, ProxyConfig config) {
        if (!(config instanceof V2RayConfig)) {
            logger.error("config must be instance of V2RayConfig");
            return false;
        }

        V2RayConfig v2rayConfig = (V2RayConfig) config;
        String configContent = v2rayConfig.toConfigString();

        logger.info("configuring v2ray...");

        // 上传配置文件
        boolean uploaded = sshService.uploadText(session, configContent, CONFIG_PATH);
        if (!uploaded) {
            logger.error("failed to upload v2ray config");
            return false;
        }

        logger.info("v2ray configured successfully");
        return true;
    }

    @Override
    public boolean start(SshSession session) {
        logger.info("starting v2ray...");

        // 启动服务
        ExecutionResult result =
            sshService.executeCommand(session, "systemctl start " + SERVICE_NAME);
        if (!result.isSuccessful()) {
            logger.error("failed to start v2ray: {}", result.getStderr());
            return false;
        }

        // 设置开机自启
        ExecutionResult enableResult =
            sshService.executeCommand(session, "systemctl enable " + SERVICE_NAME);
        if (!enableResult.isSuccessful()) {
            logger.warn("failed to enable v2ray auto-start: {}", enableResult.getStderr());
        }

        logger.info("v2ray started successfully");
        return true;
    }

    @Override
    public boolean stop(SshSession session) {
        logger.info("stopping v2ray...");

        // 停止服务
        ExecutionResult result =
            sshService.executeCommand(session, "systemctl stop " + SERVICE_NAME);
        if (!result.isSuccessful()) {
            logger.error("failed to stop v2ray: {}", result.getStderr());
            return false;
        }

        logger.info("v2ray stopped successfully");
        return true;
    }

    @Override
    public boolean uninstall(SshSession session) {
        logger.info("uninstalling v2ray...");

        // 停止服务
        stop(session);

        // 卸载V2Ray
        ExecutionResult result =
            sshService.executeCommand(
                session,
                "bash <(curl -L"
                    + " https://raw.githubusercontent.com/v2fly/fhs-install-v2ray/master/install-release.sh)"
                    + " --remove");
        if (!result.isSuccessful()) {
            logger.error("failed to uninstall v2ray: {}", result.getStderr());
            return false;
        }

        logger.info("v2ray uninstalled successfully");
        return true;
    }

    @Override
    public ProxyStatus getStatus(SshSession session) {
        logger.debug("checking v2ray status...");

        // 检查是否安装
        ExecutionResult checkInstall =
            sshService.executeCommand(
                session, "command -v v2ray >/dev/null 2>&1 && echo 'yes' || echo 'no'");
        if (!checkInstall.isSuccessful() || "no".equals(checkInstall.getStdout().trim())) {
            return ProxyStatus.NOT_INSTALLED;
        }

        // 检查服务状态
        ExecutionResult result =
            sshService.executeCommand(session, "systemctl is-active " + SERVICE_NAME);
        String status = result.getStdout().trim();

        if ("active".equals(status)) {
            return ProxyStatus.RUNNING;
        }

        else if ("inactive".equals(status) || "failed".equals(status)) {
            return ProxyStatus.STOPPED;
        }

        else {
            return ProxyStatus.UNKNOWN;
        }

    }

    @Override
    public String getConfigFilePath(SshSession session) {
        return CONFIG_PATH;
    }

    @Override
    public String getVersion(SshSession session) {
        logger.debug("checking v2ray version...");

        ExecutionResult result = sshService.executeCommand(session, "v2ray --version");
        if (!result.isSuccessful()) {
            logger.error("failed to get v2ray version: {}", result.getStderr());
            return "unknown";
        }

        String output = result.getStdout().trim();

        // 示例输出: "V2Ray 4.45.2 (V2Fly, a community-driven edition of V2Ray.)"
        if (output.contains("V2Ray")) {
            String[] parts = output.split("\\s+");
            if (parts.length >= 2) {
                return parts[1];
            }

        }

        return output;
    }

    /**
     * 重启服务
     *
     * @param session SSH会话
     * @return 是否成功
     */
    private boolean restart(SshSession session) {
        logger.info("restarting v2ray...");

        // 重启服务
        ExecutionResult result =
            sshService.executeCommand(session, "systemctl restart " + SERVICE_NAME);
        if (!result.isSuccessful()) {
            logger.error("failed to restart v2ray: {}", result.getStderr());
            return false;
        }

        logger.info("v2ray restarted successfully");
        return true;
    }

}