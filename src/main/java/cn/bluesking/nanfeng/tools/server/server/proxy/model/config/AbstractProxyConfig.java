package cn.bluesking.nanfeng.tools.server.server.proxy.model.config;

/**
 * 代理配置抽象基类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
public abstract class AbstractProxyConfig extends ProxyConfig {

/**
     * 代理 ID
     */
    private String id;

    /**
     * 服务器地址
     */
    private String server;

    /**
     * 服务器端口
     */
    private int port;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否自动启动
     */
    private boolean autoStart;

    /**
     * 默认构造函数
     */
    public AbstractProxyConfig() {
        this.autoStart = true;
}

    /**
     * 构造函数
     *
     * @param id 代理 ID
     * @param port 端口
     */
    public AbstractProxyConfig(String id, int port) {
        this.id = id;
        this.port = port;
        this.autoStart = true;
}

    @Override
    public String getId() {
        return id;
}

    @Override
    public void setId(String id) {
        this.id = id;
}

    @Override
    public String getServer() {
        return server;
}

    @Override
    public void setServer(String server) {
        this.server = server;
}

    @Override
    public int getPort() {
        return port;
}

    @Override
    public void setPort(int port) {
        this.port = port;
}

    @Override
    public String getRemark() {
        return remark;
}

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
}

    @Override
    public boolean isAutoStart() {
        return autoStart;
}

    @Override
    public void setAutoStart(boolean autoStart) {
        this.autoStart = autoStart;

}

}