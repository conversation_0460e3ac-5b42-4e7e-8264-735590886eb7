package cn.bluesking.nanfeng.tools.server.server.proxy.model.config;

import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyType;

/**
 * 简单代理配置，仅用于在工厂中初步解析JSON获取代理类型。
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
class SimpleProxyConfig extends AbstractProxyConfig {

    /**
     * 代理类型
     */
    private ProxyType type;

    /**
     * 默认构造函数
     */
    public SimpleProxyConfig() {
        super();
    }

    /**
     * 带参数的构造函数
     *
     * @param type 代理类型
     */
    public SimpleProxyConfig(ProxyType type) {
        super();
        this.type = type;
    }

    @Override
    public ProxyType getType() {
        return type;
    }

    /**
     * 设置代理类型
     *
     * @param type 代理类型
     */
    public void setType(ProxyType type) {
        this.type = type;
    }

    @Override
    public String toConfigString() {
        throw new UnsupportedOperationException("SimpleProxyConfig不支持生成配置字符串");
    }

    public String toJson() {
        throw new UnsupportedOperationException("SimpleProxyConfig不支持生成JSON字符串");

    }

}