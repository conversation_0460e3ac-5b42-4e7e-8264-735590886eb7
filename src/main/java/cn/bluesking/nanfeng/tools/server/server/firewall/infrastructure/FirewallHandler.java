package cn.bluesking.nanfeng.tools.server.server.firewall.infrastructure;

import java.util.List;

import cn.bluesking.nanfeng.tools.server.server.firewall.model.FirewallType;
import cn.bluesking.nanfeng.tools.server.server.firewall.model.PortInfo;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;

/**
 * 防火墙处理器接口
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
public interface FirewallHandler {

    /**
     * 获取防火墙类型
     *
     * @return 防火墙类型
     */
    FirewallType getType();

    /**
     * 开放端口
     *
     * @param session SSH会话
     * @param port 端口号
     * @param protocol 协议
     * @return 是否成功
     */
    boolean openPort(SshSession session, int port, String protocol);

    /**
     * 关闭端口
     *
     * @param session SSH会话
     * @param port 端口号
     * @param protocol 协议
     * @return 是否成功
     */
    boolean closePort(SshSession session, int port, String protocol);

    /**
     * 获取已开放的端口列表
     *
     * @param session SSH会话
     * @return 端口列表
     */
    List<PortInfo> getOpenedPorts(SshSession session);

    /**
     * 检查端口是否开放
     *
     * @param session SSH会话
     * @param port 端口号
     * @param protocol 协议
     * @return 是否开放
     */
    boolean isPortOpen(SshSession session, int port, String protocol);

    /**
     * 重启防火墙
     *
     * @param session SSH会话
     * @return 是否成功
     */
    boolean restart(SshSession session);

    /**
     * 添加富规则
     *
     * @param session SSH会话
     * @param rule 规则
     * @return 是否成功
     */
    boolean addRichRule(SshSession session, String rule);

    /**
     * 移除富规则
     *
     * @param session SSH会话
     * @param rule 规则
     * @return 是否成功
     */
    boolean removeRichRule(SshSession session, String rule);

    /**
     * 检查防火墙是否已安装
     *
     * @param session SSH会话
     * @return 是否已安装
     */
    boolean isInstalled(SshSession session);

    /**
     * 安装防火墙
     *
     * @param session SSH会话
     * @return 是否成功
     */
    boolean install(SshSession session);
}