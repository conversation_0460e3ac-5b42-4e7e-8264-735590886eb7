package cn.bluesking.nanfeng.tools.server.server.ssh.service;

import cn.bluesking.nanfeng.tools.server.server.ssh.model.ExecutionResult;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;

/**
 * SSH服务接口
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public interface SshService {

    /**
     * 使用密码连接SSH
     *
     * @param host 主机地址
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @return SSH会话
     */
    SshSession connect(String host, int port, String username, String password);

    /**
     * 使用密钥连接SSH
     *
     * @param host 主机地址
     * @param port 端口
     * @param username 用户名
     * @param privateKey 私钥
     * @return SSH会话
     */
    SshSession connectWithKey(String host, int port, String username, String privateKey);

    /**
     * 执行命令
     *
     * @param session SSH会话
     * @param command 命令
     * @return 执行结果
     */
    ExecutionResult executeCommand(SshSession session, String command);

    /**
     * 执行命令（带超时）
     *
     * @param session SSH会话
     * @param command 命令
     * @param timeout 超时时间（毫秒）
     * @return 执行结果
     */
    ExecutionResult executeCommandWithTimeout(SshSession session, String command, long timeout);

    /**
     * 上传文件
     *
     * @param session SSH会话
     * @param localPath 本地路径
     * @param remotePath 远程路径
     * @return 是否成功
     */
    boolean uploadFile(SshSession session, String localPath, String remotePath);

    /**
     * 下载文件
     *
     * @param session SSH会话
     * @param remotePath 远程路径
     * @param localPath 本地路径
     * @return 是否成功
     */
    boolean downloadFile(SshSession session, String remotePath, String localPath);

    /**
     * 上传文本内容
     *
     * @param session SSH会话
     * @param content 文本内容
     * @param remotePath 远程路径
     * @return 是否成功
     */
    boolean uploadText(SshSession session, String content, String remotePath);

    /**
     * 下载文本内容
     *
     * @param session SSH会话
     * @param remotePath 远程路径
     * @return 文本内容
     */
    String downloadText(SshSession session, String remotePath);

    /**
     * 断开连接
     *
     * @param session SSH会话
     */
    void disconnect(SshSession session);

    /**
     * 检查是否已连接
     *
     * @param session SSH会话
     * @return 是否已连接
     */
    boolean isConnected(SshSession session);
}