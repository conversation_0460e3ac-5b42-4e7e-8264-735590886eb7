package cn.bluesking.nanfeng.tools.server.server.proxy.model.config;

import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyType;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * V2Ray配置类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
public class V2RayConfig extends ProxyConfig {

    /**
     * 协议
     */
    private String protocol;

    /**
     * UUID
     */
    private String uuid;

    /**
     * 加密方式
     */
    private String security;

    /**
     * 网络类型
     */
    private String network;

    /**
     * 伪装类型
     */
    private String type;

    /**
     * 伪装域名
     */
    private String host;

    /**
     * 伪装路径
     */
    private String path;

    /**
     * 是否启用TLS
     */
    private boolean enableTls;

    /**
     * TLS域名
     */
    private String tlsServerName;

    /**
     * 构造函数
     */
    public V2RayConfig() {
        this.protocol = "vmess";
        this.uuid = UUID.randomUUID().toString();
        this.security = "auto";
        this.network = "tcp";
        this.type = "none";
        this.path = "/";
        this.enableTls = false;
    }

    @Override
    @JsonIgnore
    public ProxyType getType() {
        return ProxyType.V2RAY;
    }

    @Override
    public String toConfigString() {
        Map<String, Object> config = new HashMap<>();
        config.put("v", "2");
        config.put("ps", getRemark());
        config.put("add", getServer());
        config.put("port", getPort());
        config.put("id", uuid);
        config.put("aid", 0);
        config.put("net", network);
        config.put("type", type);
        config.put("host", host);
        config.put("path", path);
        config.put("tls", enableTls ? "tls" : "");
        config.put("sni", tlsServerName);

        try {
            return new ObjectMapper().writeValueAsString(config);
        }

        catch (JsonProcessingException e) {
            return "{}";
        }

    }

    /*-------------------- getter --------------------*/

    public String getProtocol() {
        return protocol;
    }

    public String getUuid() {
        return uuid;
    }

    public String getSecurity() {
        return security;
    }

    public String getNetwork() {
        return network;
    }

    public String getHeaderType() {
        return type;
    }

    public String getHost() {
        return host;
    }

    public String getPath() {
        return path;
    }

    public boolean isEnableTls() {
        return enableTls;
    }

    public String getTlsServerName() {
        return tlsServerName;
    }

    /*-------------------- setter --------------------*/

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public void setSecurity(String security) {
        this.security = security;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public void setHeaderType(String type) {
        this.type = type;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public void setEnableTls(boolean enableTls) {
        this.enableTls = enableTls;
    }

    public void setTlsServerName(String tlsServerName) {
        this.tlsServerName = tlsServerName;
    }

}