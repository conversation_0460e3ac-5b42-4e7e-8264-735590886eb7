package cn.bluesking.nanfeng.tools.server.server.ssh.model;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * SSH会话模型类
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public class SshSession implements AutoCloseable {

    /** 会话对象 */
    private Object session;

    /** 主机地址 */
    private String host;

    /** 端口 */
    private int port;

    /** 用户名 */
    private String username;

    /** 是否已连接 */
    private boolean connected;

    /** 连接时间 */
    private LocalDateTime connectTime;

    /** 最后活动时间 */
    private LocalDateTime lastActiveTime;

    /** 超时时间（毫秒） */
    private int timeoutMillis;

    /*-------------------- constructor --------------------*/

    /** 默认构造函数 */
    public SshSession() {
        this.timeoutMillis = 30000;
        this.connectTime = LocalDateTime.now();
        this.lastActiveTime = LocalDateTime.now();
    }

/**
     * 带参数构造函数
     *
     * @param session 会话对象
     * @param host 主机地址
     * @param port 端口
     * @param username 用户名
     */
    public SshSession(Object session, String host, int port, String username) {
        this();
        this.session = session;
        this.host = host;
        this.port = port;
        this.username = username;
        this.connected = true;
        this.connectTime = LocalDateTime.now();
        this.lastActiveTime = LocalDateTime.now();
        this.timeoutMillis = 30000; // 默认30秒
    }

/*-------------------- getter --------------------*/

    public Object getSession() {
        return session;
    }

public String getHost() {
        return host;
    }

public int getPort() {
        return port;
    }

public String getUsername() {
        return username;
    }

public boolean isConnected() {
        return connected;
    }

public LocalDateTime getConnectTime() {
        return connectTime;
    }

public LocalDateTime getLastActiveTime() {
        return lastActiveTime;
    }

public int getTimeoutMillis() {
        return timeoutMillis;
    }

/*-------------------- setter --------------------*/

    public void setSession(Object session) {
        this.session = session;
    }

public void setHost(String host) {
        this.host = host;
    }

public void setPort(int port) {
        this.port = port;
    }

public void setUsername(String username) {
        this.username = username;
    }

public void setConnected(boolean connected) {
        this.connected = connected;
    }

public void setConnectTime(LocalDateTime connectTime) {
        this.connectTime = connectTime;
    }

public void setLastActiveTime(LocalDateTime lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

public void setTimeoutMillis(int timeoutMillis) {
        this.timeoutMillis = timeoutMillis;
    }

/*-------------------- public method --------------------*/

    /** 更新最后活动时间 */
    public void updateLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
    }

/**
     * 检查会话是否超时
     *
     * @return 是否超时
     */
    public boolean isTimeout() {
        if (timeoutMillis <= 0) {
            return false;
        }

long seconds = java.time.Duration.between(lastActiveTime, LocalDateTime.now()).getSeconds();
        return seconds * 1000 > timeoutMillis;
    }

/** 关闭会话 */
    @Override
    public void close() {
        // 实际关闭操作由SshService实现
        // 此处为了支持try-with-resources语法
    }

/*-------------------- equals and hashCode --------------------*/

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SshSession that = (SshSession) o;
        return port == that.port
                && Objects.equals(host, that.host)
                && Objects.equals(username, that.username);
    }

@Override
    public int hashCode() {
        return Objects.hash(host, port, username);
    }

/*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "SshSession{"
                + "host='"
                + host
                + '\''
                + ", port="
                + port
                + ", username='"
                + username
                + '\''
                + ", connected="
                + connected
                + ", connectTime="
                + connectTime
                + ", lastActiveTime="
                + lastActiveTime
                + '}';
    }

}