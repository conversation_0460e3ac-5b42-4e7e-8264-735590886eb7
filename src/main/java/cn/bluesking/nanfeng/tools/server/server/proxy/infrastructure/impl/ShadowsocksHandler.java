package cn.bluesking.nanfeng.tools.server.server.proxy.infrastructure.impl;

import cn.bluesking.nanfeng.tools.server.server.proxy.model.config.ShadowsocksConfig;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.bluesking.nanfeng.tools.server.server.proxy.infrastructure.ProxyHandler;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyStatus;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyType;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.config.ProxyConfig;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.ExecutionResult;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;
import cn.bluesking.nanfeng.tools.server.server.ssh.service.SshService;

/**
 * Shadowsocks代理处理器实现类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
@Component
public class ShadowsocksHandler implements ProxyHandler {

    private static final Logger logger = LoggerFactory.getLogger(ShadowsocksHandler.class);
    private static final String CONFIG_PATH = "/etc/shadowsocks-libev/config.json";
    private static final String SERVICE_NAME = "shadowsocks-libev";
    private static final Pattern VERSION_PATTERN = Pattern.compile("shadowsocks-libev ([0-9\\.]+)");

    @Autowired
    private SshService sshService;

    @Override
    public ProxyType getType() {
        return ProxyType.SHADOWSOCKS;
    }

    @Override
    public boolean install(SshSession session, ProxyConfig config) {
        if (!(config instanceof ShadowsocksConfig)) {
            logger.error("config must be instance of ShadowsocksConfig");
            return false;
        }

        logger.info("installing shadowsocks-libev...");

        // 检测操作系统类型
        String osDetectCommand =
            "if [ -f /etc/os-release ]; then . /etc/os-release; echo $ID; else echo unknown;"
                + " fi";
        ExecutionResult osResult = sshService.executeCommand(session, osDetectCommand);

        String osId = osResult.isSuccessful() ? osResult.getStdout().trim() : "unknown";
        String installCommand;

        // 根据操作系统类型执行不同的安装命令
        switch(osId) {
            case "ubuntu":
            case "debian":
                installCommand = "apt-get update && apt-get install -y shadowsocks-libev";
                break;
            case "centos":
            case "rhel":
            case "fedora":
                installCommand =
                    "yum install -y epel-release && " + "yum install -y shadowsocks-libev";
                break;
            default:
                logger.error("unsupported OS: {}", osId);
                return false;
        }

        // 执行安装命令
        ExecutionResult result = sshService.executeCommand(session, installCommand);
        if (!result.isSuccessful()) {
            logger.error("failed to install shadowsocks-libev: {}", result.getStderr());
            return false;
        }

        // 配置Shadowsocks
        if (!configure(session, config)) {
            logger.error("failed to configure shadowsocks");
            return false;
        }

        // 启动Shadowsocks
        if (!start(session)) {
            logger.error("failed to start shadowsocks");
            return false;
        }

        logger.info("shadowsocks installed and started successfully");
        return true;
    }

    @Override
    public boolean update(SshSession session) {
        logger.info("updating shadowsocks-libev...");

        // 检测操作系统类型
        String osDetectCommand =
            "if [ -f /etc/os-release ]; then . /etc/os-release; echo $ID; else echo unknown;"
                + " fi";
        ExecutionResult osResult = sshService.executeCommand(session, osDetectCommand);

        String osId = osResult.isSuccessful() ? osResult.getStdout().trim() : "unknown";
        String updateCommand;

        // 根据操作系统类型执行不同的更新命令
        switch(osId) {
            case "ubuntu":
            case "debian":
                updateCommand = "apt-get update && apt-get upgrade -y shadowsocks-libev";
                break;
            case "centos":
            case "rhel":
            case "fedora":
                updateCommand = "yum update -y shadowsocks-libev";
                break;
            default:
                logger.error("unsupported OS: {}", osId);
                return false;
        }

        // 执行更新命令
        ExecutionResult result = sshService.executeCommand(session, updateCommand);
        if (!result.isSuccessful()) {
            logger.error("failed to update shadowsocks-libev: {}", result.getStderr());
            return false;
        }

        // 如果正在运行，则重启服务
        if (getStatus(session) == ProxyStatus.RUNNING) {
            if (!restart(session)) {
                logger.error("failed to restart shadowsocks after update");
                return false;
            }

        }

        logger.info("shadowsocks updated successfully");
        return true;
    }

    @Override
    public boolean configure(SshSession session, ProxyConfig config) {
        if (!(config instanceof ShadowsocksConfig)) {
            logger.error("config must be instance of ShadowsocksConfig");
            return false;
        }

        ShadowsocksConfig ssConfig = (ShadowsocksConfig) config;
        String configContent = ssConfig.toConfigString();

        logger.info("configuring shadowsocks...");

        // 确保配置目录存在
        ExecutionResult mkdirResult =
            sshService.executeCommand(
                session,
                "mkdir -p " + CONFIG_PATH.substring(0, CONFIG_PATH.lastIndexOf('/')));

        if (!mkdirResult.isSuccessful()) {
            logger.error("failed to create config directory: {}", mkdirResult.getStderr());
            return false;
        }

        // 上传配置文件
        boolean uploaded = sshService.uploadText(session, configContent, CONFIG_PATH);
        if (!uploaded) {
            logger.error("failed to upload shadowsocks config");
            return false;
        }

        logger.info("shadowsocks configured successfully");
        return true;
    }

    @Override
    public boolean start(SshSession session) {
        logger.info("starting shadowsocks...");

        // 启动服务
        ExecutionResult result =
            sshService.executeCommand(session, "systemctl start " + SERVICE_NAME);
        if (!result.isSuccessful()) {
            logger.error("failed to start shadowsocks: {}", result.getStderr());
            return false;
        }

        // 设置开机自启
        ExecutionResult enableResult =
            sshService.executeCommand(session, "systemctl enable " + SERVICE_NAME);
        if (!enableResult.isSuccessful()) {
            logger.warn("failed to enable shadowsocks auto-start: {}", enableResult.getStderr());
        }

        logger.info("shadowsocks started successfully");
        return true;
    }

    @Override
    public boolean stop(SshSession session) {
        logger.info("stopping shadowsocks...");

        // 停止服务
        ExecutionResult result =
            sshService.executeCommand(session, "systemctl stop " + SERVICE_NAME);
        if (!result.isSuccessful()) {
            logger.error("failed to stop shadowsocks: {}", result.getStderr());
            return false;
        }

        logger.info("shadowsocks stopped successfully");
        return true;
    }

    @Override
    public boolean uninstall(SshSession session) {
        logger.info("uninstalling shadowsocks...");

        // 先停止服务
        stop(session);

        // 检测操作系统类型
        String osDetectCommand =
            "if [ -f /etc/os-release ]; then . /etc/os-release; echo $ID; else echo unknown;"
                + " fi";
        ExecutionResult osResult = sshService.executeCommand(session, osDetectCommand);

        String osId = osResult.isSuccessful() ? osResult.getStdout().trim() : "unknown";
        String uninstallCommand;

        // 根据操作系统类型执行不同的卸载命令
        switch(osId) {
            case "ubuntu":
            case "debian":
                uninstallCommand = "apt-get purge -y shadowsocks-libev";
                break;
            case "centos":
            case "rhel":
            case "fedora":
                uninstallCommand = "yum remove -y shadowsocks-libev";
                break;
            default:
                logger.error("unsupported OS: {}", osId);
                return false;
        }

        // 执行卸载命令
        ExecutionResult result = sshService.executeCommand(session, uninstallCommand);
        if (!result.isSuccessful()) {
            logger.error("failed to uninstall shadowsocks: {}", result.getStderr());
            return false;
        }

        // 清理配置文件
        ExecutionResult cleanResult =
            sshService.executeCommand(session, "rm -rf /etc/shadowsocks-libev");
        if (!cleanResult.isSuccessful()) {
            logger.warn(
                "failed to remove shadowsocks config directory: {}", cleanResult.getStderr());
        }

        logger.info("shadowsocks uninstalled successfully");
        return true;
    }

    @Override
    public ProxyStatus getStatus(SshSession session) {
        logger.debug("checking shadowsocks status...");

        // 检查是否安装
        ExecutionResult checkInstall =
            sshService.executeCommand(
                session, "command -v ss-base >/dev/null 2>&1 && echo 'yes' || echo 'no'");
        if (!checkInstall.isSuccessful() || "no".equals(checkInstall.getStdout().trim())) {
            return ProxyStatus.NOT_INSTALLED;
        }

        // 检查服务状态
        ExecutionResult result =
            sshService.executeCommand(session, "systemctl is-active " + SERVICE_NAME);
        String status = result.getStdout().trim();

        if ("active".equals(status)) {
            return ProxyStatus.RUNNING;
        }

        else if ("inactive".equals(status) || "failed".equals(status)) {
            return ProxyStatus.STOPPED;
        }

        else {
            return ProxyStatus.UNKNOWN;
        }

    }

    @Override
    public String getConfigFilePath(SshSession session) {
        return CONFIG_PATH;
    }

    @Override
    public String getVersion(SshSession session) {
        logger.debug("checking shadowsocks version...");

        ExecutionResult result = sshService.executeCommand(session, "ss-base --version");
        if (!result.isSuccessful()) {
            logger.error("failed to get shadowsocks version: {}", result.getStderr());
            return "unknown";
        }

        String output = result.getStdout().isEmpty() ? result.getStderr() : result.getStdout();
        Matcher matcher = VERSION_PATTERN.matcher(output);

        if (matcher.find()) {
            return matcher.group(1);
        }

        return "unknown";
    }

    /**
     * 重启服务
     *
     * @param session SSH会话
     * @return 是否成功
     */
    private boolean restart(SshSession session) {
        logger.info("restarting shadowsocks...");

        // 重启服务
        ExecutionResult result =
            sshService.executeCommand(session, "systemctl restart " + SERVICE_NAME);
        if (!result.isSuccessful()) {
            logger.error("failed to restart shadowsocks: {}", result.getStderr());
            return false;
        }

        logger.info("shadowsocks restarted successfully");
        return true;
    }

}