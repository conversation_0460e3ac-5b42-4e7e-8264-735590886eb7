package cn.bluesking.nanfeng.tools.server.server.base.model;

import cn.bluesking.nanfeng.tools.common.db.IdentityEnum;
import cn.bluesking.nanfeng.tools.common.db.converter.IdentityEnumAttributeConverter;

/**
 * 服务器状态枚举。
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public enum ServerStatus implements IdentityEnum {

    /** 在线状态。 */
    ONLINE(1, "在线"),

    /** 离线状态。 */
    OFFLINE(2, "离线"),

    /** 未知状态。 */
    UNKNOWN(3, "未知"),

    /** 连接中状态。 */
    CONNECTING(4, "连接中"),

    /** 维护中状态。 */
    MAINTENANCE(5, "维护中");

    private final Integer id;

    private final String desc;

    ServerStatus(Integer id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    /*-------------------- getter --------------------*/

    @Override
    public Integer getId() {
        return id;
    }

    public String getDesc() {
        return desc;
    }

    /*-------------------- inner class --------------------*/

    public static class Converter extends IdentityEnumAttributeConverter<ServerStatus> {
        public Converter() {
            super(ServerStatus.values());
        }
    }

}