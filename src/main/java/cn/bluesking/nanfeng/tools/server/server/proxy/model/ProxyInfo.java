package cn.bluesking.nanfeng.tools.server.server.proxy.model;

import cn.bluesking.nanfeng.tools.common.utils.JsonUtils.JsonIgnore;
import cn.bluesking.nanfeng.tools.server.server.base.model.ServerInfo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import java.time.LocalDateTime;
import java.util.Objects;
import jakarta.persistence.Convert;

/**
 * 代理信息实体类。
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
@Entity
public class ProxyInfo {

    /**
     * 代理 ID。
     */
    @Id
    @SequenceGenerator(
        name = "proxy_info_sequence",
        sequenceName = "proxy_info_sequence",
        allocationSize = 1
    )
    @GeneratedValue(
        strategy = GenerationType.SEQUENCE,
        generator = "proxy_info_sequence"
    )
    private Long id;

    /**
     * 代理类型。
     */
    @Column(nullable = false, columnDefinition = "int2")
    @Convert(converter = ProxyType.Converter.class)
    private ProxyType proxyType;

    /**
     * 本地端口。
     */
    @Column(nullable = false)
    private Integer localPort;

    /**
     * 远程端口。
     */
    @Column(nullable = false)
    private Integer remotePort;

    /**
     * 远程主机。
     */
    @Column(nullable = false)
    private String remoteHost;

    /**
     * 描述。
     */
    @Column
    private String description;

    /**
     * 是否启用。
     */
    @Column
    private Boolean enabled;

    /**
     * 创建时间。
     */
    @Column
    private LocalDateTime createdAt = LocalDateTime.now();

    @ManyToOne(
        fetch = FetchType.LAZY,
        optional = false
    )
    @JsonIgnore
    private ServerInfo serverInfo;

    /*--------------------- getter ---------------------*/

    public Long getId() {
        return id;
    }

    public ProxyType getProxyType() {
        return proxyType;
    }

    public Integer getLocalPort() {
        return localPort;
    }

    public Integer getRemotePort() {
        return remotePort;
    }

    public String getRemoteHost() {
        return remoteHost;
    }

    public String getDescription() {
        return description;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public ServerInfo getServerInfo() {
        return serverInfo;
    }

    /*--------------------- setter ---------------------*/

    public void setId(Long id) {
        this.id = id;
    }

    public void setProxyType(ProxyType type) {
        this.proxyType = type;
    }

    public void setLocalPort(Integer localPort) {
        this.localPort = localPort;
    }

    public void setRemotePort(Integer remotePort) {
        this.remotePort = remotePort;
    }

    public void setRemoteHost(String remoteHost) {
        this.remoteHost = remoteHost;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public void setServerInfo(ServerInfo serverInfo) {
        this.serverInfo = serverInfo;
    }

    /*--------------------- equals and hashCode ---------------------*/

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ProxyInfo proxyInfo = (ProxyInfo) o;
        return Objects.equals(id, proxyInfo.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    /*--------------------- toString ---------------------*/

    @Override
    public String toString() {
        return "{\"ProxyInfo\":{" +
               "\"id\": " + id +
               ", \"proxyType\": \"" + proxyType + '\"' +
               ", \"localPort\": " + localPort +
               ", \"remotePort\": " + remotePort +
               ", \"remoteHost\": \"" + remoteHost + '\"' +
               ", \"description\": \"" + description + '\"' +
               ", \"enabled\": " + enabled +
               ", \"createdAt\": \"" + createdAt + '\"' +
               "}}";
    }

}