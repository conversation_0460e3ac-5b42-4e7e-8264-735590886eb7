package cn.bluesking.nanfeng.tools.server.server.base.service.impl;

import cn.bluesking.nanfeng.tools.server.server.base.model.ServerInfo;
import cn.bluesking.nanfeng.tools.server.server.base.model.ServerStatus;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 服务器信息数据访问接口。
 *
 * <AUTHOR>
 * @date 2025-06-01
 * @since 2.2.0
 */
@Repository
interface ServerInfoRepository extends JpaRepository<ServerInfo, Long> {

    /**
     * 根据服务器状态查询服务器列表。
     *
     * @param status 服务器状态
     * @return 服务器列表
     */
    List<ServerInfo> findByStatus(ServerStatus status);

    /**
     * 根据服务器名称查询服务器列表。
     *
     * @param name 服务器名称
     * @return 服务器列表
     */
    List<ServerInfo> findByNameContaining(String name);

    /**
     * 根据主机地址查询服务器。
     *
     * @param host 主机地址
     * @return 服务器
     */
    ServerInfo findByHost(String host);

    /**
     * 根据是否自动重连查询服务器列表。
     *
     * @param autoReconnect 是否自动重连
     * @return 服务器列表
     */
    List<ServerInfo> findByAutoReconnect(boolean autoReconnect);

}