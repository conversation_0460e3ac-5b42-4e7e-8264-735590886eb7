package cn.bluesking.nanfeng.tools.server.server.proxy.service;

import java.util.List;

import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyInfo;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyStatus;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.config.ProxyConfig;

/**
 * 代理服务接口
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public interface ProxyService {

    /**
     * 安装代理软件
     *
     * @param serverId 服务器ID
     * @param config 代理配置
     * @return 是否成功
     */
    boolean install(Long serverId, ProxyConfig config);

    /**
     * 更新代理软件
     *
     * @param serverId 服务器ID
     * @param proxyId 代理ID
     * @return 是否成功
     */
    boolean update(Long serverId, String proxyId);

    /**
     * 配置代理软件
     *
     * @param serverId 服务器ID
     * @param proxyId 代理ID
     * @param config 代理配置
     * @return 是否成功
     */
    boolean configure(Long serverId, String proxyId, ProxyConfig config);

    /**
     * 启动代理服务
     *
     * @param serverId 服务器ID
     * @param proxyId 代理ID
     * @return 是否成功
     */
    boolean start(Long serverId, String proxyId);

    /**
     * 停止代理服务
     *
     * @param serverId 服务器ID
     * @param proxyId 代理ID
     * @return 是否成功
     */
    boolean stop(Long serverId, String proxyId);

    /**
     * 卸载代理软件
     *
     * @param serverId 服务器ID
     * @param proxyId 代理ID
     * @return 是否成功
     */
    boolean uninstall(Long serverId, String proxyId);

    /**
     * 获取代理状态
     *
     * @param serverId 服务器ID
     * @param proxyId 代理ID
     * @return 代理状态
     */
    ProxyStatus getStatus(Long serverId, String proxyId);

    /**
     * 获取服务器上的所有代理
     *
     * @param serverId 服务器ID
     * @return 代理信息列表
     */
    List<ProxyInfo> getProxiesByServer(Long serverId);

    /**
     * 导出代理配置
     *
     * @param serverId 服务器ID
     * @param proxyId 代理ID
     * @param format 配置格式
     * @return 配置内容
     */
    String exportConfig(Long serverId, String proxyId, String format);

    /**
     * 生成二维码
     *
     * @param serverId 服务器ID
     * @param proxyId 代理ID
     * @param filePath 文件路径
     * @return 是否成功
     */
    boolean generateQRCode(Long serverId, String proxyId, String filePath);
}