package cn.bluesking.nanfeng.tools.server.server.firewall.infrastructure.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.bluesking.nanfeng.tools.server.server.firewall.infrastructure.FirewallHandler;
import cn.bluesking.nanfeng.tools.server.server.firewall.model.FirewallType;
import cn.bluesking.nanfeng.tools.server.server.firewall.model.PortInfo;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.ExecutionResult;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;
import cn.bluesking.nanfeng.tools.server.server.ssh.service.SshService;

/**
 * Iptables防火墙处理器实现类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
@Component
public class IptablesHandler implements FirewallHandler {

    private static final Logger logger = LoggerFactory.getLogger(IptablesHandler.class);
    private static final Pattern PORT_PATTERN = Pattern.compile("dpt:(\\d+)");

    @Autowired
    private SshService sshService;

    @Override
    public FirewallType getType() {
        return FirewallType.IPTABLES;
    }

    @Override
    public boolean openPort(SshSession session, int port, String protocol) {
        logger.info("opening port {}/{} with iptables", port, protocol);

        String command =
            String.format(
                "iptables -A INPUT -p %s --dport %d -j ACCEPT && iptables-save >"
                    + " /etc/iptables/rules.v4 || { mkdir -p /etc/iptables && iptables-save"
                    + " > /etc/iptables/rules.v4; }",
                protocol.toLowerCase(), port);

        ExecutionResult result = sshService.executeCommand(session, command);

        if (result.isSuccessful()) {
            logger.info("port {}/{} opened successfully", port, protocol);
            return true;
        }

        else {
            logger.error("failed to open port {}/{}: {}", port, protocol, result.getStderr());
            return false;
        }

    }

    @Override
    public boolean closePort(SshSession session, int port, String protocol) {
        logger.info("closing port {}/{} with iptables", port, protocol);

        String command =
            String.format(
                "iptables -D INPUT -p %s --dport %d -j ACCEPT && iptables-save >"
                    + " /etc/iptables/rules.v4 || { mkdir -p /etc/iptables && iptables-save"
                    + " > /etc/iptables/rules.v4; }",
                protocol.toLowerCase(), port);

        ExecutionResult result = sshService.executeCommand(session, command);

        if (result.isSuccessful()) {
            logger.info("port {}/{} closed successfully", port, protocol);
            return true;
        }

        else {
            logger.error("failed to close port {}/{}: {}", port, protocol, result.getStderr());
            return false;
        }

    }

    @Override
    public List<PortInfo> getOpenedPorts(SshSession session) {
        logger.info("getting opened ports with iptables");

        String command = "iptables -L INPUT -n | grep ACCEPT";
        ExecutionResult result = sshService.executeCommand(session, command);

        List<PortInfo> ports = new ArrayList<>();

        if (result.isSuccessful()) {
            String output = result.getStdout().trim();
            if (!output.isEmpty()) {
                String[] lines = output.split("\n");
                for (String line : lines) {
                    String protocol = "";
                    if (line.contains("tcp")) {
                        protocol = "tcp";
                    }

                    else if (line.contains("udp")) {
                        protocol = "udp";
                    }

                    else {
                        continue;
                    }

                    Matcher matcher = PORT_PATTERN.matcher(line);
                    if (matcher.find()) {
                        int port = Integer.parseInt(matcher.group(1));
                        ports.add(new PortInfo(port, protocol, true));
                    }

                }

            }

            logger.info("found {} opened ports", ports.size());
        }

        else {
            logger.error("failed to get opened ports: {}", result.getStderr());
        }

        return ports;
    }

    @Override
    public boolean isPortOpen(SshSession session, int port, String protocol) {
        logger.info("checking if port {}/{} is open with iptables", port, protocol);

        String command =
            String.format(
                "iptables -L INPUT -n | grep -E '%s.*dpt:%d.*ACCEPT'",
                protocol.toLowerCase(), port);

        ExecutionResult result = sshService.executeCommand(session, command);

        boolean isOpen = result.isSuccessful() && !result.getStdout().trim().isEmpty();
        logger.info("port {}/{} is {}", port, protocol, isOpen ? "open" : "closed");

        return isOpen;
    }

    @Override
    public boolean restart(SshSession session) {
        logger.info("restarting iptables");

        // Iptables 没有服务，需要重新加载规则
        String command = "iptables-restore < /etc/iptables/rules.v4 || echo 'No rules file found'";
        ExecutionResult result = sshService.executeCommand(session, command);

        if (result.isSuccessful() && !result.getStdout().contains("No rules file found")) {
            logger.info("iptables rules reloaded successfully");
            return true;
        }

        else {
            logger.warn("no iptables rules file found or failed to reload");
            // 即使没有规则文件，也不算失败
            return true;
        }

    }

    @Override
    public boolean addRichRule(SshSession session, String rule) {
        logger.info("iptables does not support rich rules directly: {}", rule);

        // Iptables 不支持富规则，需要转换为 iptables 命令
        // 这里只是一个简单实现，实际应该根据富规则的内容进行转换
        return false;
    }

    @Override
    public boolean removeRichRule(SshSession session, String rule) {
        logger.info("iptables does not support rich rules directly: {}", rule);

        // Iptables 不支持富规则，需要转换为 iptables 命令
        // 这里只是一个简单实现，实际应该根据富规则的内容进行转换
        return false;
    }

    @Override
    public boolean isInstalled(SshSession session) {
        logger.info("checking if iptables is installed");

        String command = "command -v iptables >/dev/null 2>&1 && echo 'yes' || echo 'no'";
        ExecutionResult result = sshService.executeCommand(session, command);

        boolean installed = result.isSuccessful() && "yes".equals(result.getStdout().trim());
        logger.info("iptables is {}", installed ? "installed" : "not installed");

        return installed;
    }

    @Override
    public boolean install(SshSession session) {
        logger.info("installing iptables");

        // 检测操作系统类型
        String osDetectCommand =
            "if [ -f /etc/os-release ]; then . /etc/os-release; echo $ID; else echo unknown;"
                + " fi";
        ExecutionResult osResult = sshService.executeCommand(session, osDetectCommand);

        String osId = osResult.isSuccessful() ? osResult.getStdout().trim() : "unknown";
        String installCommand;

        // 根据操作系统类型执行不同的安装命令
        switch(osId) {
            case "ubuntu":
            case "debian":
                installCommand =
                    "apt-get update && apt-get install -y iptables iptables-persistent";
                break;
            case "centos":
            case "rhel":
            case "fedora":
                installCommand =
                    "yum install -y iptables-services && systemctl enable iptables && systemctl"
                        + " start iptables";
                break;
            default:
                logger.error("unsupported OS: {}", osId);
                return false;
        }

        // 执行安装命令
        ExecutionResult result = sshService.executeCommand(session, installCommand);

        if (result.isSuccessful()) {
            logger.info("iptables installed successfully");

            // 创建保存规则的目录
            String mkdirCommand = "mkdir -p /etc/iptables";
            sshService.executeCommand(session, mkdirCommand);

            // 设置基本规则
            String setupCommand =
                "iptables -A INPUT -i lo -j ACCEPT && "
                    + "iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT && "
                    + "iptables -A INPUT -p tcp --dport 22 -j ACCEPT && "
                    + "iptables-save > /etc/iptables/rules.v4";

            ExecutionResult setupResult = sshService.executeCommand(session, setupCommand);

            if (setupResult.isSuccessful()) {
                logger.info("basic iptables rules set up successfully");
                return true;
            }

            else {
                logger.warn("failed to set up basic iptables rules: {}", setupResult.getStderr());
                // 即使设置基本规则失败，安装也算成功
                return true;
            }

        }

        else {
            logger.error("failed to install iptables: {}", result.getStderr());
            return false;
        }

    }
}