package cn.bluesking.nanfeng.tools.server.server.base.model;

import cn.bluesking.nanfeng.tools.common.db.IdentityEnum;
import cn.bluesking.nanfeng.tools.common.db.converter.IdentityEnumAttributeConverter;

/**
 * 操作系统类型枚举。
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
public enum OsType implements IdentityEnum {

    /**
     * CentOS 系统。
     */
    CENTOS(1, "CentOS"),

    /**
     * Ubuntu 系统。
     */
    UBUNTU(2, "Ubuntu"),

    /**
     * Debian 系统。
     */
    DEBIAN(3, "Debian"),

    /**
     * Fedora 系统。
     */
    FEDORA(4, "Fedora"),

    /**
     * Alpine 系统。
     */
    ALPINE(5, "Alpine"),

    /**
     * 其他系统。
     */
    OTHER(6, "Unknow");

    private final Integer id;

    private final String desc;

    OsType(Integer id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    /*-------------------- getter --------------------*/

    @Override
    public Integer getId() {
        return id;
    }

    public String getDesc() {
        return desc;
    }

    /*-------------------- public static method --------------------*/

    /**
     * 根据操作系统信息判断操作系统类型。
     *
     * @param osInfo 操作系统信息
     * @return 操作系统类型
     */
    public static OsType detect(String osInfo) {

        if (osInfo == null || osInfo.isEmpty()) {
            return OTHER;
        }

        String lowerInfo = osInfo.toLowerCase();
        if (lowerInfo.contains("centos")) {
            return CENTOS;
        }
        else if (lowerInfo.contains("ubuntu")) {
            return UBUNTU;
        }
        else if (lowerInfo.contains("debian")) {
            return DEBIAN;
        }
        else if (lowerInfo.contains("fedora")) {
            return FEDORA;
        }
        else if (lowerInfo.contains("alpine")) {
            return ALPINE;
        }
        else {
            return OTHER;
        }
    }

    /*-------------------- inner class --------------------*/

    public static class Converter extends IdentityEnumAttributeConverter<OsType> {
        public Converter() {
            super(OsType.values());
        }
    }

}