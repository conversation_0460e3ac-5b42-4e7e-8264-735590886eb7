package cn.bluesking.nanfeng.tools.server.server.firewall.infrastructure.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.bluesking.nanfeng.tools.server.server.firewall.infrastructure.FirewallHandler;
import cn.bluesking.nanfeng.tools.server.server.firewall.model.FirewallType;
import cn.bluesking.nanfeng.tools.server.server.firewall.model.PortInfo;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.ExecutionResult;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;
import cn.bluesking.nanfeng.tools.server.server.ssh.service.SshService;

/**
 * Firewalld防火墙处理器实现类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
@Component
public class FirewalldHandler implements FirewallHandler {

    private static final Logger logger = LoggerFactory.getLogger(FirewalldHandler.class);
    private static final Pattern PORT_PATTERN = Pattern.compile("(\\d+)/(tcp|udp)");

    @Autowired
    private SshService sshService;

    @Override
    public FirewallType getType() {
        return FirewallType.FIREWALLD;
    }

    @Override
    public boolean openPort(SshSession session, int port, String protocol) {
        logger.info("opening port {}/{} with firewalld", port, protocol);

        String command =
            String.format(
                "firewall-cmd --permanent --add-port=%d/%s && firewall-cmd --reload",
                port, protocol.toLowerCase());
        ExecutionResult result = sshService.executeCommand(session, command);

        if (result.isSuccessful()) {
            logger.info("port {}/{} opened successfully", port, protocol);
            return true;
        }

        else {
            logger.error("failed to open port {}/{}: {}", port, protocol, result.getStderr());
            return false;
        }

    }

    @Override
    public boolean closePort(SshSession session, int port, String protocol) {
        logger.info("closing port {}/{} with firewalld", port, protocol);

        String command =
            String.format(
                "firewall-cmd --permanent --remove-port=%d/%s && firewall-cmd --reload",
                port, protocol.toLowerCase());
        ExecutionResult result = sshService.executeCommand(session, command);

        if (result.isSuccessful()) {
            logger.info("port {}/{} closed successfully", port, protocol);
            return true;
        }

        else {
            logger.error("failed to close port {}/{}: {}", port, protocol, result.getStderr());
            return false;
        }

    }

    @Override
    public List<PortInfo> getOpenedPorts(SshSession session) {
        logger.info("getting opened ports with firewalld");

        String command = "firewall-cmd --list-ports";
        ExecutionResult result = sshService.executeCommand(session, command);

        List<PortInfo> ports = new ArrayList<>();

        if (result.isSuccessful()) {
            String output = result.getStdout().trim();
            if (!output.isEmpty()) {
                String[] portStrings = output.split("\\s+");
                for (String portString : portStrings) {
                    Matcher matcher = PORT_PATTERN.matcher(portString);
                    if (matcher.matches()) {
                        int port = Integer.parseInt(matcher.group(1));
                        String protocol = matcher.group(2);
                        ports.add(new PortInfo(port, protocol, true));
                    }

                }

            }

            logger.info("found {} opened ports", ports.size());
        }

        else {
            logger.error("failed to get opened ports: {}", result.getStderr());
        }

        return ports;
    }

    @Override
    public boolean isPortOpen(SshSession session, int port, String protocol) {
        logger.info("checking if port {}/{} is open with firewalld", port, protocol);

        String command =
            String.format(
                "firewall-cmd --list-ports | grep -w \"%d/%s\"",
                port, protocol.toLowerCase());
        ExecutionResult result = sshService.executeCommand(session, command);

        boolean isOpen = result.isSuccessful() && !result.getStdout().trim().isEmpty();
        logger.info("port {}/{} is {}", port, protocol, isOpen ? "open" : "closed");

        return isOpen;
    }

    @Override
    public boolean restart(SshSession session) {
        logger.info("restarting firewalld");

        String command = "systemctl restart firewalld";
        ExecutionResult result = sshService.executeCommand(session, command);

        if (result.isSuccessful()) {
            logger.info("firewalld restarted successfully");
            return true;
        }

        else {
            logger.error("failed to restart firewalld: {}", result.getStderr());
            return false;
        }

    }

    @Override
    public boolean addRichRule(SshSession session, String rule) {
        logger.info("adding rich rule: {}", rule);

        String command =
            String.format(
                "firewall-cmd --permanent --add-rich-rule='%s' && firewall-cmd --reload",
                rule);
        ExecutionResult result = sshService.executeCommand(session, command);

        if (result.isSuccessful()) {
            logger.info("rich rule added successfully");
            return true;
        }

        else {
            logger.error("failed to add rich rule: {}", result.getStderr());
            return false;
        }

    }

    @Override
    public boolean removeRichRule(SshSession session, String rule) {
        logger.info("removing rich rule: {}", rule);

        String command =
            String.format(
                "firewall-cmd --permanent --remove-rich-rule='%s' && firewall-cmd --reload",
                rule);
        ExecutionResult result = sshService.executeCommand(session, command);

        if (result.isSuccessful()) {
            logger.info("rich rule removed successfully");
            return true;
        }

        else {
            logger.error("failed to remove rich rule: {}", result.getStderr());
            return false;
        }

    }

    @Override
    public boolean isInstalled(SshSession session) {
        logger.info("checking if firewalld is installed");

        String command = "command -v firewall-cmd >/dev/null 2>&1 && echo 'yes' || echo 'no'";
        ExecutionResult result = sshService.executeCommand(session, command);

        boolean installed = result.isSuccessful() && "yes".equals(result.getStdout().trim());
        logger.info("firewalld is {}", installed ? "installed" : "not installed");

        return installed;
    }

    @Override
    public boolean install(SshSession session) {
        logger.info("installing firewalld");

        // 检测操作系统类型
        String osDetectCommand =
            "if [ -f /etc/os-release ]; then . /etc/os-release; echo $ID; else echo unknown;"
                + " fi";
        ExecutionResult osResult = sshService.executeCommand(session, osDetectCommand);

        String osId = osResult.isSuccessful() ? osResult.getStdout().trim() : "unknown";
        String installCommand;

        // 根据操作系统类型执行不同的安装命令
        switch(osId) {
            case "ubuntu":
            case "debian":
                installCommand = "apt-get update && apt-get install -y firewalld";
                break;
            case "centos":
            case "rhel":
            case "fedora":
                installCommand = "yum install -y firewalld";
                break;
            default:
                logger.error("unsupported OS: {}", osId);
                return false;
        }

        // 执行安装命令
        ExecutionResult installResult = sshService.executeCommand(session, installCommand);
        if (!installResult.isSuccessful()) {
            logger.error("failed to install firewalld: {}", installResult.getStderr());
            return false;
        }

        // 启用并启动服务
        String enableCommand = "systemctl enable firewalld && systemctl start firewalld";
        ExecutionResult enableResult = sshService.executeCommand(session, enableCommand);

        if (enableResult.isSuccessful()) {
            logger.info("firewalld installed and started successfully");
            return true;
        }

        else {
            logger.error("failed to enable/start firewalld: {}", enableResult.getStderr());
            return false;
        }

    }
}