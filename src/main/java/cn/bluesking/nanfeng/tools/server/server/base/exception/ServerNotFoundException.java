package cn.bluesking.nanfeng.tools.server.server.base.exception;

/**
 * 服务器未找到异常
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
public class ServerNotFoundException extends ServerModuleException {

    /**
     * 构造函数
     *
     * @param serverId 服务器 ID
     */
    public ServerNotFoundException(String serverId) {
        super("服务器未找到: " + serverId);
    }

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public ServerNotFoundException(String message, Throwable cause) {
        super(404, message, cause);
    }

}