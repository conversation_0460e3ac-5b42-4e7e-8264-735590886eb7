package cn.bluesking.nanfeng.tools.server.server.base.service;

import java.util.List;
import java.util.Map;

import cn.bluesking.nanfeng.tools.server.server.base.model.ServerInfo;
import cn.bluesking.nanfeng.tools.server.server.base.model.ServerStatus;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.ExecutionResult;

/**
 * 服务器服务接口。
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public interface ServerInfoService {

    /*-------------------- base management --------------------*/

    /**
     * 添加服务器。
     *
     * @param serverInfo 服务器信息
     * @return 添加后的服务器信息
     */
    ServerInfo addServer(ServerInfo serverInfo);

    /**
     * 删除服务器。
     *
     * @param id 服务器 ID
     * @return 是否成功
     */
    boolean removeServer(Long id);

    /**
     * 获取所有服务器。
     *
     * @return 服务器列表
     */
    List<ServerInfo> getAllServers();

    /**
     * 根据 ID 获取服务器。
     *
     * @param serverId 服务器 ID
     * @return 服务器信息
     */
    ServerInfo getServerById(Long serverId);

    /*-------------------- command execution --------------------*/

    /**
     * 执行命令。
     *
     * @param id 服务器 ID
     * @param command 命令
     * @return 执行结果
     */
    ExecutionResult executeCommand(Long id, String command);

    /**
     * 批量执行命令。
     *
     * @param ids 服务器 ID 列表
     * @param command 命令
     * @return 执行结果（服务器 ID -> 执行结果）
     */
    Map<Long, ExecutionResult> batchExecuteCommand(List<Long> ids, String command);

    /*-------------------- key management --------------------*/

    /**
     * 保存服务器密钥。
     *
     * @param id 服务器 ID
     * @param privateKey 私钥内容
     * @return 是否成功
     */
    boolean saveServerKey(Long id, String privateKey);

    /**
     * 删除服务器密钥。
     *
     * @param id 服务器 ID
     * @return 是否成功
     */
    boolean removeServerKey(Long id);

    /*-------------------- connectivity --------------------*/

    /**
     * 检查服务器连接。
     *
     * @param id 服务器 ID
     * @return 是否连接成功
     */
    boolean checkServerConnection(Long id);

    /*-------------------- status management --------------------*/

    /**
     * 更新服务器状态。
     *
     * @param id 服务器 ID
     * @return 更新后的状态
     */
    ServerStatus updateServerStatus(Long id);

    /**
     * 批量更新服务器状态。
     *
     * @param ids 服务器 ID 列表
     * @return 更新后的状态（服务器 ID -> 状态）
     */
    Map<Long, ServerStatus> batchUpdateServerStatus(List<Long> ids);

}