package cn.bluesking.nanfeng.tools.server.server.proxy.model;

import cn.bluesking.nanfeng.tools.common.db.IdentityEnum;
import cn.bluesking.nanfeng.tools.common.db.converter.IdentityEnumAttributeConverter;

/**
 * 代理类型枚举
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public enum ProxyType implements IdentityEnum {
    /** V2Ray代理 */
    V2RAY(1),

    /** Shadowsocks 代理 */
    SHADOWSOCKS(2),

    /** Hysteria2 代理 */
    HYSTERIA2(3),

    /** Trojan代理 */
    TROJAN(4),

    /** WireGuard代理 */
    WIREGUARD(5),

    /** OpenVPN代理 */
    OPENVPN(6),

    /** 未知代理 */
    UNKNOWN(0);

    private final Integer id;

    ProxyType(Integer id) {
        this.id = id;
    }

    @Override
    public Integer getId() {
        return id;
    }
    
    /**
     * ProxyType 的 JPA 属性转换器。
     */
    @jakarta.persistence.Converter(autoApply = true)
    public static class Converter extends IdentityEnumAttributeConverter<ProxyType> {
        public Converter() {
            super(ProxyType.values());
        }
    }

}