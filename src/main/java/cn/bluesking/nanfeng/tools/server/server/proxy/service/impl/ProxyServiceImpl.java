package cn.bluesking.nanfeng.tools.server.server.proxy.service.impl;

import cn.bluesking.nanfeng.tools.server.server.base.exception.ServerNotFoundException;
import cn.bluesking.nanfeng.tools.server.server.base.model.ServerInfo;
import cn.bluesking.nanfeng.tools.server.server.base.service.ServerInfoService;
import cn.bluesking.nanfeng.tools.server.server.proxy.exception.ProxyOperationException;
import cn.bluesking.nanfeng.tools.server.server.proxy.exception.UnsupportedProxyTypeException;
import cn.bluesking.nanfeng.tools.server.server.proxy.infrastructure.ProxyHandler;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyInfo;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyStatus;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyType;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.config.ProxyConfig;
import cn.bluesking.nanfeng.tools.server.server.proxy.service.ProxyService;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;
import cn.bluesking.nanfeng.tools.server.server.ssh.service.SshService;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 代理服务实现类
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
@Service
public class ProxyServiceImpl implements ProxyService {

    private static final Logger logger = LoggerFactory.getLogger(ProxyServiceImpl.class);

    private final SshService sshService;
    private final ServerInfoService serverInfoService;
    private final Map<ProxyType, ProxyHandler> proxyHandlers;

    /**
     * 构造函数
     *
     * @param sshService SSH服务
     * @param serverInfoService 服务器服务
     * @param handlers 代理处理器列表
     */
    @Autowired
    public ProxyServiceImpl(
        SshService sshService, ServerInfoService serverInfoService, List<ProxyHandler> handlers) {
        this.sshService = sshService;
        this.serverInfoService = serverInfoService;
        this.proxyHandlers =
                handlers.stream()
                        .collect(Collectors.toMap(ProxyHandler::getType, handler -> handler));
    }

@Override
    public boolean install(Long serverId, ProxyConfig config) {
        logger.info("installing proxy on base: {}, type: {}", serverId, config.getType());

        ServerInfo server = getServerById(serverId);
        ProxyHandler handler = getHandlerByType(config.getType());

        try (SshSession session = connectToServer(server)) {
            boolean result = handler.install(session, config);

            if (result) {
                // TODO: 保存代理信息到数据库
                logger.info("proxy installed successfully: {}", config.getId());
            }

else {
                logger.error("failed to install proxy: {}", config.getId());
            }

return result;
        }

catch (Exception e) {
            logger.error("error installing proxy: {}", e.getMessage(), e);
            throw new ProxyOperationException("安装代理失败: " + e.getMessage(), e);
        }

}

@Override
    public boolean update(Long serverId, String proxyId) {
        logger.info("updating proxy: {} on base: {}", proxyId, serverId);

        ServerInfo server = getServerById(serverId);
        ProxyInfo proxyInfo = getProxyById(proxyId);
        ProxyHandler handler = getHandlerByType(proxyInfo.getProxyType());

        try (SshSession session = connectToServer(server)) {
            boolean result = handler.update(session);

            if (result) {
                logger.info("proxy updated successfully: {}", proxyId);
            }

else {
                logger.error("failed to update proxy: {}", proxyId);
            }

return result;
        }

catch (Exception e) {
            logger.error("error updating proxy: {}", e.getMessage(), e);
            throw new ProxyOperationException("更新代理失败: " + e.getMessage(), e);
        }

}

@Override
    public boolean configure(Long serverId, String proxyId, ProxyConfig config) {
        logger.info("configuring proxy: {} on base: {}", proxyId, serverId);

        ServerInfo server = getServerById(serverId);
        ProxyHandler handler = getHandlerByType(config.getType());

        try (SshSession session = connectToServer(server)) {
            boolean result = handler.configure(session, config);

            if (result) {
                // TODO: 更新代理配置信息到数据库
                logger.info("proxy configured successfully: {}", proxyId);
            }

else {
                logger.error("failed to configure proxy: {}", proxyId);
            }

return result;
        }

catch (Exception e) {
            logger.error("error configuring proxy: {}", e.getMessage(), e);
            throw new ProxyOperationException("配置代理失败: " + e.getMessage(), e);
        }

}

@Override
    public boolean start(Long serverId, String proxyId) {
        logger.info("starting proxy: {} on base: {}", proxyId, serverId);

        ServerInfo server = getServerById(serverId);
        ProxyInfo proxyInfo = getProxyById(proxyId);
        ProxyHandler handler = getHandlerByType(proxyInfo.getProxyType());

        try (SshSession session = connectToServer(server)) {
            boolean result = handler.start(session);

            if (result) {
                // TODO: 更新代理状态到数据库
                logger.info("proxy started successfully: {}", proxyId);
            }

else {
                logger.error("failed to start proxy: {}", proxyId);
            }

return result;
        }

catch (Exception e) {
            logger.error("error starting proxy: {}", e.getMessage(), e);
            throw new ProxyOperationException("启动代理失败: " + e.getMessage(), e);
        }

}

@Override
    public boolean stop(Long serverId, String proxyId) {
        logger.info("stopping proxy: {} on base: {}", proxyId, serverId);

        ServerInfo server = getServerById(serverId);
        ProxyInfo proxyInfo = getProxyById(proxyId);
        ProxyHandler handler = getHandlerByType(proxyInfo.getProxyType());

        try (SshSession session = connectToServer(server)) {
            boolean result = handler.stop(session);

            if (result) {
                // TODO: 更新代理状态到数据库
                logger.info("proxy stopped successfully: {}", proxyId);
            }

else {
                logger.error("failed to stop proxy: {}", proxyId);
            }

return result;
        }

catch (Exception e) {
            logger.error("error stopping proxy: {}", e.getMessage(), e);
            throw new ProxyOperationException("停止代理失败: " + e.getMessage(), e);
        }

}

@Override
    public boolean uninstall(Long serverId, String proxyId) {
        logger.info("uninstalling proxy: {} on base: {}", proxyId, serverId);

        ServerInfo server = getServerById(serverId);
        ProxyInfo proxyInfo = getProxyById(proxyId);
        ProxyHandler handler = getHandlerByType(proxyInfo.getProxyType());

        try (SshSession session = connectToServer(server)) {
            boolean result = handler.uninstall(session);

            if (result) {
                // TODO: 从数据库中删除代理信息
                logger.info("proxy uninstalled successfully: {}", proxyId);
            }

else {
                logger.error("failed to uninstall proxy: {}", proxyId);
            }

return result;
        }

catch (Exception e) {
            logger.error("error uninstalling proxy: {}", e.getMessage(), e);
            throw new ProxyOperationException("卸载代理失败: " + e.getMessage(), e);
        }

}

@Override
    public ProxyStatus getStatus(Long serverId, String proxyId) {
        logger.debug("getting status of proxy: {} on base: {}", proxyId, serverId);

        ServerInfo server = getServerById(serverId);
        ProxyInfo proxyInfo = getProxyById(proxyId);
        ProxyHandler handler = getHandlerByType(proxyInfo.getProxyType());

        try (SshSession session = connectToServer(server)) {
            ProxyStatus status = handler.getStatus(session);

            // TODO: 更新代理状态到数据库
            logger.debug("proxy status: {} for {}", status, proxyId);

            return status;
        }

catch (Exception e) {
            logger.error("error getting proxy status: {}", e.getMessage(), e);
            throw new ProxyOperationException("获取代理状态失败: " + e.getMessage(), e);
        }

}

@Override
    public List<ProxyInfo> getProxiesByServer(Long serverId) {
        logger.debug("getting all proxies for base: {}", serverId);

        // TODO: 从数据库中获取服务器上的所有代理信息

        return List.of(); // 临时返回空列表
    }

@Override
    public String exportConfig(Long serverId, String proxyId, String format) {
        logger.info(
                "exporting config for proxy: {} on base: {}, format: {}",
                proxyId,
                serverId,
                format);

        // TODO: 实现导出配置功能

        return ""; // 临时返回空字符串
    }

@Override
    public boolean generateQRCode(Long serverId, String proxyId, String filePath) {
        logger.info(
                "generating QR code for proxy: {} on base: {}, path: {}",
                proxyId,
                serverId,
                filePath);

        // TODO: 实现生成二维码功能

        return false; // 临时返回失败
    }

/*-------------------- private method --------------------*/

    /**
     * 根据ID获取服务器信息
     *
     * @param serverId 服务器ID
     * @return 服务器信息
     */
    private ServerInfo getServerById(Long serverId) {
        ServerInfo server = serverInfoService.getServerById(serverId);
        if (server == null) {
            throw new ServerNotFoundException("服务器不存在: " + serverId);
        }

return server;
    }

/**
     * 根据ID获取代理信息
     *
     * @param proxyId 代理ID
     * @return 代理信息
     */
    private ProxyInfo getProxyById(String proxyId) {
        // TODO: 从数据库中获取代理信息

        // 临时返回一个空的代理信息
        ProxyInfo proxyInfo = new ProxyInfo();
        proxyInfo.setId(Long.valueOf(proxyId));
        proxyInfo.setProxyType(ProxyType.V2RAY); // 默认类型

        return proxyInfo;
    }

/**
     * 根据类型获取代理处理器
     *
     * @param type 代理类型
     * @return 代理处理器
     */
    private ProxyHandler getHandlerByType(ProxyType type) {
        ProxyHandler handler = proxyHandlers.get(type);
        if (handler == null) {
            throw new UnsupportedProxyTypeException("不支持的代理类型: " + type);
        }

return handler;
    }

/**
     * 连接到服务器
     *
     * @param server 服务器信息
     * @return SSH会话
     */
    private SshSession connectToServer(ServerInfo server) {
        if (server.getPrivateKey() != null && !server.getPrivateKey().isEmpty()) {
            return sshService.connectWithKey(
                    server.getHost(),
                    server.getPort(),
                    server.getUsername(),
                    server.getPrivateKey());
        }

else {
            return sshService.connect(
                    server.getHost(), server.getPort(), server.getUsername(), server.getPassword());
        }

}
}