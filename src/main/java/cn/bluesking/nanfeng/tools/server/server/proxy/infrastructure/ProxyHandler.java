package cn.bluesking.nanfeng.tools.server.server.proxy.infrastructure;

import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyStatus;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyType;
import cn.bluesking.nanfeng.tools.server.server.proxy.model.config.ProxyConfig;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;

/**
 * 代理处理器接口
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public interface ProxyHandler {

    /**
     * 获取代理类型
     *
     * @return 代理类型
     */
    ProxyType getType();

    /**
     * 安装代理软件
     *
     * @param session SSH会话
     * @param config 代理配置
     * @return 是否成功
     */
    boolean install(SshSession session, ProxyConfig config);

    /**
     * 更新代理软件
     *
     * @param session SSH会话
     * @return 是否成功
     */
    boolean update(SshSession session);

    /**
     * 配置代理软件
     *
     * @param session SSH会话
     * @param config 代理配置
     * @return 是否成功
     */
    boolean configure(SshSession session, ProxyConfig config);

    /**
     * 启动代理服务
     *
     * @param session SSH会话
     * @return 是否成功
     */
    boolean start(SshSession session);

    /**
     * 停止代理服务
     *
     * @param session SSH会话
     * @return 是否成功
     */
    boolean stop(SshSession session);

    /**
     * 卸载代理软件
     *
     * @param session SSH会话
     * @return 是否成功
     */
    boolean uninstall(SshSession session);

    /**
     * 获取代理状态
     *
     * @param session SSH会话
     * @return 代理状态
     */
    ProxyStatus getStatus(SshSession session);

    /**
     * 获取配置文件路径
     *
     * @param session SSH会话
     * @return 配置文件路径
     */
    String getConfigFilePath(SshSession session);

    /**
     * 获取代理软件版本
     *
     * @param session SSH会话
     * @return 版本信息
     */
    String getVersion(SshSession session);
}