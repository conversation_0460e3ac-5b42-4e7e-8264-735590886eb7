package cn.bluesking.nanfeng.tools.server.server.firewall.model;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 端口信息实体类
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public class PortInfo {

    /** 端口ID */
    private String id;

    /** 服务器ID */
    private Long serverId;

    /** 端口号 */
    private int port;

    /** 协议类型 */
    private String protocol;

    /** 端口描述 */
    private String description;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;

    /** 是否开放 */
    private boolean open;

    /*-------------------- constructor --------------------*/

    /** 默认构造函数 */
    public PortInfo() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

/**
     * 带参数构造函数
     *
     * @param id 端口ID
     * @param serverId 服务器ID
     * @param port 端口号
     * @param protocol 协议类型
     */
    public PortInfo(String id, Long serverId, int port, String protocol) {
        this.id = id;
        this.serverId = serverId;
        this.port = port;
        this.protocol = protocol;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.open = false;
    }

/**
     * 完整参数构造函数
     *
     * @param id 端口ID
     * @param serverId 服务器ID
     * @param port 端口号
     * @param protocol 协议类型
     * @param description 端口描述
     */
    public PortInfo(String id, Long serverId, int port, String protocol, String description) {
        this.id = id;
        this.serverId = serverId;
        this.port = port;
        this.protocol = protocol;
        this.description = description;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.open = false;
    }

/**
     * 构造函数
     *
     * @param port 端口号
     * @param protocol 协议
     */
    public PortInfo(int port, String protocol) {
        this.port = port;
        this.protocol = protocol;
        this.open = false;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

/**
     * 构造函数
     *
     * @param port 端口号
     * @param protocol 协议
     * @param open 是否开放
     */
    public PortInfo(int port, String protocol, boolean open) {
        this.port = port;
        this.protocol = protocol;
        this.open = open;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

/**
     * 构造函数
     *
     * @param port 端口号
     * @param protocol 协议
     * @param description 描述
     * @param open 是否开放
     */
    public PortInfo(int port, String protocol, String description, boolean open) {
        this.port = port;
        this.protocol = protocol;
        this.description = description;
        this.open = open;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

/*-------------------- getter --------------------*/

    public String getId() {
        return id;
    }

public Long getServerId() {
        return serverId;
    }

public int getPort() {
        return port;
    }

public String getProtocol() {
        return protocol;
    }

public String getDescription() {
        return description;
    }

public LocalDateTime getCreateTime() {
        return createTime;
    }

public LocalDateTime getUpdateTime() {
        return updateTime;
    }

public boolean isOpen() {
        return open;
    }

/*-------------------- setter --------------------*/

    public void setId(String id) {
        this.id = id;
    }

public void setServerId(Long serverId) {
        this.serverId = serverId;
    }

public void setPort(int port) {
        this.port = port;
    }

public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

public void setDescription(String description) {
        this.description = description;
    }

public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

public void setOpen(boolean open) {
        this.open = open;
    }

/*-------------------- equals and hashCode --------------------*/

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PortInfo portInfo = (PortInfo) o;
        return port == portInfo.port
                && protocol.equals(portInfo.protocol)
                && Objects.equals(serverId, portInfo.serverId);
    }

@Override
    public int hashCode() {
        return Objects.hash(serverId, port, protocol);
    }

/*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "PortInfo{"
                + "id='"
                + id
                + '\''
                + ", serverId='"
                + serverId
                + '\''
                + ", port="
                + port
                + ", protocol='"
                + protocol
                + '\''
                + ", description='"
                + description
                + '\''
                + ", createTime="
                + createTime
                + ", updateTime="
                + updateTime
                + ", open="
                + open
                + '}';
    }

}