package cn.bluesking.nanfeng.tools.server.server.firewall.service;

import java.util.List;

import cn.bluesking.nanfeng.tools.server.server.firewall.model.FirewallType;
import cn.bluesking.nanfeng.tools.server.server.firewall.model.PortInfo;

/**
 * 防火墙服务接口
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public interface FirewallService {

    /**
     * 开放端口
     *
     * @param serverId 服务器ID
     * @param port 端口号
     * @param protocol 协议
     * @return 是否成功
     */
    boolean openPort(Long serverId, int port, String protocol);

    /**
     * 关闭端口
     *
     * @param serverId 服务器ID
     * @param port 端口号
     * @param protocol 协议
     * @return 是否成功
     */
    boolean closePort(Long serverId, int port, String protocol);

    /**
     * 获取已开放的端口列表
     *
     * @param serverId 服务器ID
     * @return 端口列表
     */
    List<PortInfo> getOpenedPorts(Long serverId);

    /**
     * 检查端口是否开放
     *
     * @param serverId 服务器ID
     * @param port 端口号
     * @param protocol 协议
     * @return 是否开放
     */
    boolean isPortOpen(Long serverId, int port, String protocol);

    /**
     * 重启防火墙
     *
     * @param serverId 服务器ID
     * @return 是否成功
     */
    boolean restartFirewall(Long serverId);

    /**
     * 自动检测并设置防火墙
     *
     * @param serverId 服务器ID
     * @return 是否成功
     */
    boolean autoDetectAndSetFirewall(Long serverId);

    /**
     * 添加富规则
     *
     * @param serverId 服务器ID
     * @param rule 规则
     * @return 是否成功
     */
    boolean addRichRule(Long serverId, String rule);

    /**
     * 移除富规则
     *
     * @param serverId 服务器ID
     * @param rule 规则
     * @return 是否成功
     */
    boolean removeRichRule(Long serverId, String rule);

    /**
     * 检测防火墙类型
     *
     * @param serverId 服务器ID
     * @return 防火墙类型
     */
    FirewallType detectFirewallType(Long serverId);
}