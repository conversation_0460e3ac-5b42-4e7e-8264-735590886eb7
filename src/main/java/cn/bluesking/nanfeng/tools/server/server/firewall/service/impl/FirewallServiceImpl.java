package cn.bluesking.nanfeng.tools.server.server.firewall.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.bluesking.nanfeng.tools.server.server.base.exception.ServerNotFoundException;
import cn.bluesking.nanfeng.tools.server.server.base.model.ServerInfo;
import cn.bluesking.nanfeng.tools.server.server.base.service.ServerInfoService;
import cn.bluesking.nanfeng.tools.server.server.firewall.exception.FirewallOperationException;
import cn.bluesking.nanfeng.tools.server.server.firewall.infrastructure.FirewallHandler;
import cn.bluesking.nanfeng.tools.server.server.firewall.model.FirewallType;
import cn.bluesking.nanfeng.tools.server.server.firewall.model.PortInfo;
import cn.bluesking.nanfeng.tools.server.server.firewall.service.FirewallService;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.ExecutionResult;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;
import cn.bluesking.nanfeng.tools.server.server.ssh.service.SshService;

/**
 * 防火墙服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
@Service
public class FirewallServiceImpl implements FirewallService {

    private static final Logger logger = LoggerFactory.getLogger(FirewallServiceImpl.class);

    @Autowired
    private ServerInfoService serverInfoService;

    @Autowired
    private SshService sshService;

    @Autowired
    private List<FirewallHandler> firewallHandlers;

    private Map<FirewallType, FirewallHandler> handlerMap;

    @PostConstruct
    public void init() {
        handlerMap =
            firewallHandlers.stream()
                .collect(Collectors.toMap(FirewallHandler::getType, handler -> handler));
    }

    @Override
    public boolean openPort(Long serverId, int port, String protocol) {
        logger.info("opening port {}/{} on base {}", port, protocol, serverId);

        try (SshSession session = getServerSession(serverId)) {
            FirewallType firewallType = detectFirewallType(serverId);
            FirewallHandler handler = getHandler(firewallType);

            boolean result = handler.openPort(session, port, protocol);
            if (result) {
                logger.info("port {}/{} opened successfully on base {}", port, protocol, serverId);
            }

            else {
                logger.error("failed to open port {}/{} on base {}", port, protocol, serverId);
            }

            return result;
        }

        catch (Exception e) {
            logger.error(
                "error opening port {}/{} on base {}: {}",
                port,
                protocol,
                serverId,
                e.getMessage());
            throw new FirewallOperationException("开放端口失败: " + e.getMessage(), e);
        }

    }

    @Override
    public boolean closePort(Long serverId, int port, String protocol) {
        logger.info("closing port {}/{} on base {}", port, protocol, serverId);

        try (SshSession session = getServerSession(serverId)) {
            FirewallType firewallType = detectFirewallType(serverId);
            FirewallHandler handler = getHandler(firewallType);

            boolean result = handler.closePort(session, port, protocol);
            if (result) {
                logger.info("port {}/{} closed successfully on base {}", port, protocol, serverId);
            }

            else {
                logger.error("failed to close port {}/{} on base {}", port, protocol, serverId);
            }

            return result;
        }

        catch (Exception e) {
            logger.error(
                "error closing port {}/{} on base {}: {}",
                port,
                protocol,
                serverId,
                e.getMessage());
            throw new FirewallOperationException("关闭端口失败: " + e.getMessage(), e);
        }

    }

    @Override
    public List<PortInfo> getOpenedPorts(Long serverId) {
        logger.info("getting opened ports on base {}", serverId);

        try (SshSession session = getServerSession(serverId)) {
            FirewallType firewallType = detectFirewallType(serverId);

            // 如果没有防火墙，返回空列表
            if (firewallType == FirewallType.NONE) {
                logger.info("no firewall detected on base {}, returning empty list", serverId);
                return Collections.emptyList();
            }

            FirewallHandler handler = getHandler(firewallType);

            List<PortInfo> ports = handler.getOpenedPorts(session);

            // 设置服务器ID
            ports.forEach(port -> port.setServerId(serverId));

            logger.info("found {} opened ports on base {}", ports.size(), serverId);
            return ports;
        }

        catch (Exception e) {
            logger.error("error getting opened ports on base {}: {}", serverId, e.getMessage());
            throw new FirewallOperationException("获取开放端口列表失败: " + e.getMessage(), e);
        }

    }

    @Override
    public boolean isPortOpen(Long serverId, int port, String protocol) {
        logger.info("checking if port {}/{} is open on base {}", port, protocol, serverId);

        try (SshSession session = getServerSession(serverId)) {
            FirewallType firewallType = detectFirewallType(serverId);

            // 如果没有防火墙，默认端口是开放的
            if (firewallType == FirewallType.NONE) {
                logger.info("no firewall detected on base {}, assuming port is open", serverId);
                return true;
            }

            FirewallHandler handler = getHandler(firewallType);

            boolean isOpen = handler.isPortOpen(session, port, protocol);

            logger.info(
                "port {}/{} is {} on base {}",
                port,
                protocol,
                isOpen ? "open" : "closed",
                serverId);
            return isOpen;
        }

        catch (Exception e) {
            logger.error(
                "error checking if port {}/{} is open on base {}: {}",
                port,
                protocol,
                serverId,
                e.getMessage());
            throw new FirewallOperationException("检查端口状态失败: " + e.getMessage(), e);
        }

    }

    @Override
    public boolean restartFirewall(Long serverId) {
        logger.info("restarting firewall on base {}", serverId);

        try (SshSession session = getServerSession(serverId)) {
            FirewallType firewallType = detectFirewallType(serverId);

            // 如果没有防火墙，不需要重启
            if (firewallType == FirewallType.NONE) {
                logger.info("no firewall detected on base {}, nothing to restart", serverId);
                return true;
            }

            FirewallHandler handler = getHandler(firewallType);

            boolean result = handler.restart(session);
            if (result) {
                logger.info("firewall restarted successfully on base {}", serverId);
            }

            else {
                logger.error("failed to restart firewall on base {}", serverId);
            }

            return result;
        }

        catch (Exception e) {
            logger.error("error restarting firewall on base {}: {}", serverId, e.getMessage());
            throw new FirewallOperationException("重启防火墙失败: " + e.getMessage(), e);
        }

    }

    @Override
    public boolean autoDetectAndSetFirewall(Long serverId) {
        logger.info("auto detecting and setting firewall on base {}", serverId);

        try (SshSession session = getServerSession(serverId)) {
            // 检测防火墙类型
            FirewallType firewallType = detectFirewallType(serverId);

            // 如果没有防火墙，尝试安装 firewalld
            if (firewallType == FirewallType.NONE) {
                logger.info(
                    "no firewall detected on base {}, attempting to install firewalld",
                    serverId);

                FirewallHandler firewalldHandler = getHandler(FirewallType.FIREWALLD);
                boolean installed = firewalldHandler.install(session);

                if (installed) {
                    logger.info("firewalld installed successfully on base {}", serverId);
                    firewallType = FirewallType.FIREWALLD;
                }

                else {
                    // 如果安装 firewalld 失败，尝试安装 iptables
                    logger.warn(
                        "failed to install firewalld on base {}, attempting to install"
                            + " iptables",
                        serverId);

                    FirewallHandler iptablesHandler = getHandler(FirewallType.IPTABLES);
                    installed = iptablesHandler.install(session);

                    if (installed) {
                        logger.info("iptables installed successfully on base {}", serverId);
                        firewallType = FirewallType.IPTABLES;
                    }

                    else {
                        logger.error("failed to install any firewall on base {}", serverId);
                        return false;
                    }

                }

            }

            // 开放SSH端口（22）确保不会被锁定
            FirewallHandler handler = getHandler(firewallType);
            boolean sshPortOpened = handler.openPort(session, 22, "tcp");

            if (!sshPortOpened) {
                logger.warn(
                    "failed to open SSH port on base {}, this may cause connectivity issues",
                    serverId);
            }

            logger.info("firewall auto-detected and set up on base {}: {}", serverId, firewallType);
            return true;
        }

        catch (Exception e) {
            logger.error(
                "error auto-detecting and setting firewall on base {}: {}",
                serverId,
                e.getMessage());
            throw new FirewallOperationException("自动配置防火墙失败: " + e.getMessage(), e);
        }

    }

    @Override
    public boolean addRichRule(Long serverId, String rule) {
        logger.info("adding rich rule on base {}: {}", serverId, rule);

        try (SshSession session = getServerSession(serverId)) {
            FirewallType firewallType = detectFirewallType(serverId);

            // 富规则主要适用于 firewalld
            if (firewallType != FirewallType.FIREWALLD) {
                logger.warn(
                    "rich rules are primarily supported by firewalld, current firewall is {}",
                    firewallType);
            }

            FirewallHandler handler = getHandler(firewallType);

            boolean result = handler.addRichRule(session, rule);
            if (result) {
                logger.info("rich rule added successfully on base {}", serverId);
            }

            else {
                logger.error("failed to add rich rule on base {}", serverId);
            }

            return result;
        }

        catch (Exception e) {
            logger.error("error adding rich rule on base {}: {}", serverId, e.getMessage());
            throw new FirewallOperationException("添加富规则失败: " + e.getMessage(), e);
        }

    }

    @Override
    public boolean removeRichRule(Long serverId, String rule) {
        logger.info("removing rich rule on base {}: {}", serverId, rule);

        try (SshSession session = getServerSession(serverId)) {
            FirewallType firewallType = detectFirewallType(serverId);

            // 富规则主要适用于 firewalld
            if (firewallType != FirewallType.FIREWALLD) {
                logger.warn(
                    "rich rules are primarily supported by firewalld, current firewall is {}",
                    firewallType);
            }

            FirewallHandler handler = getHandler(firewallType);

            boolean result = handler.removeRichRule(session, rule);
            if (result) {
                logger.info("rich rule removed successfully on base {}", serverId);
            }

            else {
                logger.error("failed to remove rich rule on base {}", serverId);
            }

            return result;
        }

        catch (Exception e) {
            logger.error("error removing rich rule on base {}: {}", serverId, e.getMessage());
            throw new FirewallOperationException("删除富规则失败: " + e.getMessage(), e);
        }

    }

    @Override
    public FirewallType detectFirewallType(Long serverId) {
        logger.info("detecting firewall type on base {}", serverId);

        try (SshSession session = getServerSession(serverId)) {
            // 检查 firewalld
            ExecutionResult firewallResult =
                sshService.executeCommand(
                    session,
                    "command -v firewall-cmd >/dev/null 2>&1 && echo 'yes' || echo 'no'");
            if (firewallResult.isSuccessful() && "yes".equals(firewallResult.getStdout().trim())) {
                // 检查 firewalld 服务状态
                ExecutionResult statusResult =
                    sshService.executeCommand(session, "systemctl is-active firewalld");
                if (statusResult.isSuccessful()
                        && "active".equals(statusResult.getStdout().trim())) {
                    logger.info("detected active firewalld on base {}", serverId);
                    return FirewallType.FIREWALLD;
                }

            }

            // 检查 iptables
            ExecutionResult iptablesResult =
                sshService.executeCommand(
                    session,
                    "command -v iptables >/dev/null 2>&1 && echo 'yes' || echo 'no'");
            if (iptablesResult.isSuccessful() && "yes".equals(iptablesResult.getStdout().trim())) {
                logger.info("detected iptables on base {}", serverId);
                return FirewallType.IPTABLES;
            }

            // 检查 ufw
            ExecutionResult ufwResult =
                sshService.executeCommand(
                    session, "command -v ufw >/dev/null 2>&1 && echo 'yes' || echo 'no'");
            if (ufwResult.isSuccessful() && "yes".equals(ufwResult.getStdout().trim())) {
                // 检查 ufw 服务状态
                ExecutionResult statusResult =
                    sshService.executeCommand(
                        session,
                        "ufw status | grep -q 'Status: active' && echo 'yes' || echo 'no'");
                if (statusResult.isSuccessful() && "yes".equals(statusResult.getStdout().trim())) {
                    logger.info("detected active ufw on base {}", serverId);
                    return FirewallType.UFW;
                }

            }

            logger.info("no firewall detected on base {}", serverId);
            return FirewallType.NONE;
        }

        catch (Exception e) {
            logger.error("error detecting firewall type on base {}: {}", serverId, e.getMessage());
            throw new FirewallOperationException("检测防火墙类型失败: " + e.getMessage(), e);
        }

    }

    /**
     * 获取服务器的SSH会话
     *
     * @param serverId 服务器ID
     * @return SSH会话
     */
    private SshSession getServerSession(Long serverId) {
        ServerInfo server = serverInfoService.getServerById(serverId);
        if (server == null) {
            throw new ServerNotFoundException("服务器未找到: " + serverId);
        }

        // 优先使用密钥连接
        if (server.getPrivateKey() != null && !server.getPrivateKey().isEmpty()) {
            return sshService.connectWithKey(
                server.getHost(),
                server.getPort(),
                server.getUsername(),
                server.getPrivateKey());
        }

        else {
            return sshService.connect(
                server.getHost(), server.getPort(), server.getUsername(), server.getPassword());
        }

    }

    /**
     * 获取防火墙处理器
     *
     * @param firewallType 防火墙类型
     * @return 防火墙处理器
     */
    private FirewallHandler getHandler(FirewallType firewallType) {
        FirewallHandler handler = handlerMap.get(firewallType);
        if (handler == null) {
            throw new FirewallOperationException("不支持的防火墙类型: " + firewallType);
        }

        return handler;
    }

}