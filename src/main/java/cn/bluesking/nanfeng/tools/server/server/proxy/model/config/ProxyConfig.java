package cn.bluesking.nanfeng.tools.server.server.proxy.model.config;

import cn.bluesking.nanfeng.tools.server.server.proxy.model.ProxyType;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

/**
 * 代理配置抽象基类
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
    @JsonSubTypes.Type(value = V2RayConfig.class, name = "v2ray"),
    @JsonSubTypes.Type(value = ShadowsocksConfig.class, name = "shadowsocks")
})
public abstract class ProxyConfig {

    /**
     * 代理ID
     */
    private String id;

    /**
     * 服务器地址
     */
    private String server;

    /**
     * 服务器端口
     */
    private int port;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否自动启动
     */
    private boolean autoStart;

    /**
     * 默认构造函数
     */
    public ProxyConfig() {
        this.autoStart = true;
    }

    /**
     * 构造函数
     *
     * @param id 代理ID
     * @param type 代理类型
     * @param port 端口
     */
    public ProxyConfig(String id, ProxyType type, int port) {
        this.id = id;
        this.port = port;
        this.autoStart = true;
    }

    /**
     * 获取代理类型
     *
     * @return 代理类型
     */
    public abstract ProxyType getType();

    /**
     * 转换为配置字符串
     *
     * @return 配置字符串
     */
    public abstract String toConfigString();

    /*-------------------- getter --------------------*/

    public String getId() {
        return id;
    }

    public String getServer() {
        return server;
    }

    public int getPort() {
        return port;
    }

    public String getRemark() {
        return remark;
    }

    public boolean isAutoStart() {
        return autoStart;
    }

    /*-------------------- setter --------------------*/

    public void setId(String id) {
        this.id = id;
    }

    public void setServer(String server) {
        this.server = server;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setAutoStart(boolean autoStart) {
        this.autoStart = autoStart;
    }

}