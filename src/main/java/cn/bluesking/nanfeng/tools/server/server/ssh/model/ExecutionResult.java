package cn.bluesking.nanfeng.tools.server.server.ssh.model;

/**
 * 命令执行结果模型类。
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public class ExecutionResult {

    /** 退出码。 */
    private int exitCode;

    /** 标准输出。 */
    private String stdout;

    /** 标准错误。 */
    private String stderr;

    /** 执行时间（毫秒）。 */
    private long executionTime;

    /** 是否超时。 */
    private boolean timedOut;

    /** 构造函数。 */
    public ExecutionResult() {
        this.exitCode = -1;
        this.stdout = "";
        this.stderr = "";
        this.executionTime = 0;
        this.timedOut = false;
    }

/**
     * 构造函数。
     *
     * @param exitCode 退出码
     * @param stdout 标准输出
     * @param stderr 标准错误
     */
    public ExecutionResult(int exitCode, String stdout, String stderr) {
        this.exitCode = exitCode;
        this.stdout = stdout != null ? stdout : "";
        this.stderr = stderr != null ? stderr : "";
        this.executionTime = 0;
        this.timedOut = false;
    }

/**
     * 构造函数。
     *
     * @param exitCode 退出码
     * @param stdout 标准输出
     * @param stderr 标准错误
     * @param executionTime 执行时间
     * @param timedOut 是否超时
     */
    public ExecutionResult(
            int exitCode, String stdout, String stderr, long executionTime, boolean timedOut) {
        this.exitCode = exitCode;
        this.stdout = stdout != null ? stdout : "";
        this.stderr = stderr != null ? stderr : "";
        this.executionTime = executionTime;
        this.timedOut = timedOut;
    }

/**
     * 判断命令是否执行成功。
     *
     * @return 是否成功
     */
    public boolean isSuccessful() {
        return exitCode == 0 && !timedOut;
    }

/*-------------------- getter --------------------*/

    public int getExitCode() {
        return exitCode;
    }

public String getStdout() {
        return stdout;
    }

public String getStderr() {
        return stderr;
    }

public long getExecutionTime() {
        return executionTime;
    }

public boolean isTimedOut() {
        return timedOut;
    }

/*-------------------- setter --------------------*/

    public void setExitCode(int exitCode) {
        this.exitCode = exitCode;
    }

public void setStdout(String stdout) {
        this.stdout = stdout != null ? stdout : "";
    }

public void setStderr(String stderr) {
        this.stderr = stderr != null ? stderr : "";
    }

public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }

public void setTimedOut(boolean timedOut) {
        this.timedOut = timedOut;
    }

/*-------------------- toString --------------------*/

    @Override
    public String toString() {
        return "ExecutionResult{"
                + "exitCode="
                + exitCode
                + ", stdout='"
                + (stdout != null ? stdout.length() + " chars" : "null")
                + '\''
                + ", stderr='"
                + (stderr != null ? stderr.length() + " chars" : "null")
                + '\''
                + ", executionTime="
                + executionTime
                + ", timedOut="
                + timedOut
                + ", successful="
                + isSuccessful()
                + '}';
    }

}