package cn.bluesking.nanfeng.tools.server.server.proxy.exception;

import cn.bluesking.nanfeng.tools.server.server.base.exception.ServerModuleException;

/**
 * 代理操作异常
 *
 * <AUTHOR>
 * @since 2.1.9
 * @date 2025-05-30
 */
public class ProxyOperationException extends ServerModuleException {

    /**
     * 构造函数
     *
     * @param message 错误信息
     */
    public ProxyOperationException(String message) {
        super(message);
    }

/**
     * 构造函数
     *
     * @param message 错误信息
     * @param cause 原因
     */
    public ProxyOperationException(String message, Throwable cause) {
        super(message, cause);
    }

}