package cn.bluesking.nanfeng.tools.server.server.base.service.impl;

import cn.bluesking.nanfeng.tools.common.utils.ValidationUtils;
import cn.bluesking.nanfeng.tools.server.server.base.service.ServerInfoService;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cn.bluesking.nanfeng.tools.server.server.base.model.ServerInfo;
import cn.bluesking.nanfeng.tools.server.server.base.model.ServerStatus;
import cn.bluesking.nanfeng.tools.server.server.base.exception.ServerNotFoundException;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.ExecutionResult;
import cn.bluesking.nanfeng.tools.server.server.ssh.model.SshSession;
import cn.bluesking.nanfeng.tools.server.server.ssh.service.SshService;

/**
 * 服务器服务接口实现
 *
 * <AUTHOR>
 * @date 2025-05-30
 * @since 2.1.9
 */
@Service
class ServerInfoServiceImpl implements ServerInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ServerInfoServiceImpl.class);

    private final ServerInfoRepository serverInfoRepository;
    private final SshService sshService;

    public ServerInfoServiceImpl(ServerInfoRepository serverInfoRepository, SshService sshService) {
        this.serverInfoRepository = serverInfoRepository;
        this.sshService = sshService;
    }

    /*--------------------- base management ---------------------*/

    /**
     * 添加服务器
     *
     * @param serverInfo 服务器信息
     * @return 添加后的服务器信息
     */
    @Override
    @Transactional
    public ServerInfo addServer(ServerInfo serverInfo) {

        ValidationUtils.assertNotNull(serverInfo, "服务器信息不能为空！");

        logger.info("adding new server: {}", serverInfo);
        
        // 设置初始状态和检查时间
        if (serverInfo.getStatus() == null) {
            serverInfo.setStatus(ServerStatus.UNKNOWN);
        }
        serverInfo.setLastCheckTime(LocalDateTime.now());
        
        return serverInfoRepository.save(serverInfo);
    }

    /**
     * 删除服务器
     *
     * @param id 服务器 ID
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean removeServer(Long id) {

        if (id == null) {
            logger.error("server id cannot be null");
            throw new IllegalArgumentException("服务器ID不能为空");
        }

        logger.info("removing server with id: {}", id);
        
        if (!serverInfoRepository.existsById(id)) {
            logger.warn("server with id {} not found", id);
            return false;
        }
        
        serverInfoRepository.deleteById(id);
        return true;
    }

    /**
     * 获取所有服务器
     *
     * @return 服务器列表
     */
    @Override
    public List<ServerInfo> getAllServers() {
        logger.info("getting all servers");
        return serverInfoRepository.findAll();
    }

    /**
     * 根据 ID 获取服务器
     *
     * @param id 服务器 ID
     * @return 服务器信息
     */
    @Override
    public ServerInfo getServerById(Long id) {
        if (id == null) {
            logger.error("server id cannot be null");
            throw new IllegalArgumentException("服务器ID不能为空");
        }

        logger.info("getting server with id: {}", id);
        
        return serverInfoRepository.findById(id)
                .orElseThrow(() -> new ServerNotFoundException("找不到ID为" + id + "的服务器"));
    }

    /*--------------------- command execution ---------------------*/

    /**
     * 执行命令
     *
     * @param id 服务器 ID
     * @param command 命令
     * @return 执行结果
     */
    @Override
    public ExecutionResult executeCommand(Long id, String command) {
        if (id == null) {
            logger.error("server id cannot be null");
            throw new IllegalArgumentException("服务器ID不能为空");
        }

        if (command == null || command.isEmpty()) {
            logger.error("command cannot be null or empty");
            throw new IllegalArgumentException("命令不能为空");
        }

        logger.info("executing command on server {}: {}", id, command);
        
        ServerInfo serverInfo = getServerById(id);
        
        if (serverInfo.getStatus() != ServerStatus.ONLINE) {
            logger.warn("server {} is not online, current status: {}", id, serverInfo.getStatus());
            return new ExecutionResult(-1, "", "Server is not online", 0, false);
        }
        
        return executeCommandOnServer(serverInfo, command);
    }

    /**
     * 在服务器上执行命令
     *
     * @param serverInfo 服务器信息
     * @param command 命令
     * @return 执行结果
     */
    private ExecutionResult executeCommandOnServer(ServerInfo serverInfo, String command) {
        SshSession session = null;
        try {
            // 根据服务器配置选择连接方式
            if (serverInfo.getPrivateKey() != null && !serverInfo.getPrivateKey().isEmpty()) {
                // 使用密钥连接
                session = sshService.connectWithKey(
                        serverInfo.getHost(),
                        serverInfo.getPort(),
                        serverInfo.getUsername(),
                        serverInfo.getPrivateKey());
            } else if (serverInfo.getPassword() != null && !serverInfo.getPassword().isEmpty()) {
                // 使用密码连接
                session = sshService.connect(
                        serverInfo.getHost(),
                        serverInfo.getPort(),
                        serverInfo.getUsername(),
                        serverInfo.getPassword());
            } else {
                logger.error("no authentication method available for server: {}", serverInfo.getId());
                return new ExecutionResult(-1, "", "No authentication method available", 0, false);
            }
            
            // 执行命令
            int timeout = serverInfo.getConnectionTimeout() != null ? serverInfo.getConnectionTimeout() : 30000;
            ExecutionResult result = sshService.executeCommandWithTimeout(session, command, timeout);
            
            // 更新服务器状态
            serverInfo.setStatus(ServerStatus.ONLINE);
            serverInfo.setLastCheckTime(LocalDateTime.now());
            serverInfoRepository.save(serverInfo);
            
            return result;
        } catch (Exception e) {
            logger.error("error executing command on server {}: {}", serverInfo.getId(), e.getMessage());
            
            // 更新服务器状态
            serverInfo.setStatus(ServerStatus.OFFLINE);
            serverInfo.setLastCheckTime(LocalDateTime.now());
            serverInfoRepository.save(serverInfo);
            
            return new ExecutionResult(-1, "", e.getMessage(), 0, false);
        } finally {
            if (session != null) {
                sshService.disconnect(session);
            }
        }
    }

    /**
     * 批量执行命令
     *
     * @param ids 服务器 ID 列表
     * @param command 命令
     * @return 执行结果（服务器 ID -> 执行结果）
     */
    @Override
    public Map<Long, ExecutionResult> batchExecuteCommand(
        List<Long> ids, String command) {
        if (ids == null || ids.isEmpty()) {
            logger.error("server ids cannot be null or empty");
            throw new IllegalArgumentException("服务器ID列表不能为空");
        }

        if (command == null || command.isEmpty()) {
            logger.error("command cannot be null or empty");
            throw new IllegalArgumentException("命令不能为空");
        }

        logger.info("batch executing command on {} servers: {}", ids.size(), command);
        
        Map<Long, ExecutionResult> results = new HashMap<>();
        for (Long serverId : ids) {
            try {
                results.put(serverId, executeCommand(serverId, command));
            } catch (Exception e) {
                logger.error("error executing command on server {}: {}", serverId, e.getMessage());
                results.put(serverId, new ExecutionResult(-1, "", e.getMessage(), 0, false));
            }
        }

        return results;
    }

    /*--------------------- key management ---------------------*/

    /**
     * 保存服务器密钥
     *
     * @param id 服务器ID
     * @param privateKey 私钥内容
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean saveServerKey(Long id, String privateKey) {
        if (id == null) {
            logger.error("server id cannot be null");
            throw new IllegalArgumentException("服务器ID不能为空");
        }

        if (privateKey == null || privateKey.isEmpty()) {
            logger.error("private key cannot be null or empty");
            throw new IllegalArgumentException("私钥内容不能为空");
        }

        logger.info("saving private key for server: {}", id);
        
        ServerInfo serverInfo = getServerById(id);
        serverInfo.setPrivateKey(privateKey);
        serverInfo.setPassword(null); // 清除密码，优先使用密钥
        serverInfoRepository.save(serverInfo);
        
        return true;
    }

    /**
     * 删除服务器密钥
     *
     * @param id 服务器ID
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean removeServerKey(Long id) {
        if (id == null) {
            logger.error("server id cannot be null");
            throw new IllegalArgumentException("服务器ID不能为空");
        }

        logger.info("removing private key for server: {}", id);
        
        ServerInfo serverInfo = getServerById(id);
        serverInfo.setPrivateKey(null);
        serverInfoRepository.save(serverInfo);
        
        return true;
    }

    /*--------------------- connectivity ---------------------*/

    /**
     * 检查服务器连接
     *
     * @param id 服务器ID
     * @return 是否连接成功
     */
    @Override
    public boolean checkServerConnection(Long id) {
        if (id == null) {
            logger.error("server id cannot be null");
            throw new IllegalArgumentException("服务器ID不能为空");
        }

        logger.info("checking connection for server: {}", id);
        
        ServerInfo serverInfo = getServerById(id);
        
        boolean isConnected = testConnection(serverInfo);
        
        // 更新服务器状态
        serverInfo.setStatus(isConnected ? ServerStatus.ONLINE : ServerStatus.OFFLINE);
        serverInfo.setLastCheckTime(LocalDateTime.now());
        serverInfoRepository.save(serverInfo);
        
        return isConnected;
    }
    
    /**
     * 测试服务器连接
     *
     * @param serverInfo 服务器信息
     * @return 是否连接成功
     */
    private boolean testConnection(ServerInfo serverInfo) {
        SshSession session = null;
        try {
            // 根据服务器配置选择连接方式
            if (serverInfo.getPrivateKey() != null && !serverInfo.getPrivateKey().isEmpty()) {
                // 使用密钥连接
                session = sshService.connectWithKey(
                        serverInfo.getHost(),
                        serverInfo.getPort(),
                        serverInfo.getUsername(),
                        serverInfo.getPrivateKey());
            } else if (serverInfo.getPassword() != null && !serverInfo.getPassword().isEmpty()) {
                // 使用密码连接
                session = sshService.connect(
                        serverInfo.getHost(),
                        serverInfo.getPort(),
                        serverInfo.getUsername(),
                        serverInfo.getPassword());
            } else {
                logger.error("no authentication method available for server: {}", serverInfo.getId());
                return false;
            }
            
            // 执行简单命令测试连接
            ExecutionResult result = sshService.executeCommand(session, "echo 'Connection test'");
            return result.isSuccessful();
        } catch (Exception e) {
            logger.error("error testing connection to server {}: {}", serverInfo.getId(), e.getMessage());
            return false;
        } finally {
            if (session != null) {
                sshService.disconnect(session);
            }
        }
    }

    /*--------------------- status management ---------------------*/

    /**
     * 更新服务器状态
     *
     * @param id 服务器ID
     * @return 更新后的状态
     */
    @Override
    @Transactional
    public ServerStatus updateServerStatus(Long id) {
        if (id == null) {
            logger.error("server id cannot be null");
            throw new IllegalArgumentException("服务器ID不能为空");
        }

        logger.info("updating status for server: {}", id);
        
        ServerInfo serverInfo = getServerById(id);
        boolean isConnected = testConnection(serverInfo);
        
        ServerStatus newStatus = isConnected ? ServerStatus.ONLINE : ServerStatus.OFFLINE;
        serverInfo.setStatus(newStatus);
        serverInfo.setLastCheckTime(LocalDateTime.now());
        serverInfoRepository.save(serverInfo);
        
        return newStatus;
    }

    /**
     * 批量更新服务器状态
     *
     * @param ids 服务器ID列表
     * @return 更新后的状态（服务器ID -> 状态）
     */
    @Override
    public Map<Long, ServerStatus> batchUpdateServerStatus(List<Long> ids) {

        if (ids == null || ids.isEmpty()) {
            logger.error("server ids cannot be null or empty");
            throw new IllegalArgumentException("服务器ID列表不能为空");
        }

        logger.info("batch updating status for {} servers", ids.size());
        
        Map<Long, ServerStatus> statusMap = new HashMap<>();
        
        for (Long id : ids) {
            try {
                ServerStatus status = updateServerStatus(id);
                statusMap.put(id, status);
            } catch (Exception e) {
                logger.error("error updating status for server {}: {}", id, e.getMessage());
                statusMap.put(id, ServerStatus.UNKNOWN);
            }
        }
        
        return statusMap;
    }

}