apply plugin: 'java-library'
apply plugin: 'io.spring.dependency-management'

dependencies {
    // 通用工具库
    api 'com.google.guava:guava'
    api 'org.apache.commons:commons-lang3'
    api 'commons-validator:commons-validator'
    api 'com.aventrix.jnanoid:jnanoid'
    api 'com.google.code.gson:gson'
    
    // 数据库相关
    api 'org.hibernate.orm:hibernate-community-dialects'
    api 'org.flywaydb:flyway-core'
    
    // 配置加密
    api 'org.jasypt:jasypt'
    
    // Spring 核心依赖
    compileOnly 'org.springframework:spring-context'
    compileOnly 'org.springframework.data:spring-data-jpa'
    
    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
} 

bootJar {
    enabled = false
}

jar {
    enabled = true
} 