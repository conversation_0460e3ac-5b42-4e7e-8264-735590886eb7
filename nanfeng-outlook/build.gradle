apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'

dependencies {
    // 依赖基础模块
    implementation project(':nanfeng-common')
    implementation project(':nanfeng-common-web')
    
    // Spring Boot 依赖
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    
    // 数据库依赖
    implementation 'org.postgresql:postgresql'
    implementation 'org.flywaydb:flyway-database-postgresql'
    
    // 配置加密
    implementation 'com.github.ulisesbocchio:jasypt-spring-boot-starter'
    
    // Aspose Email 依赖
    implementation fileTree(dir: '../libs', includes: ['*.jar'])
    implementation 'org.javassist:javassist'
    
    // 邮件相关依赖
    implementation 'org.simplejavamail:simple-java-mail'
    implementation 'ch.astorm:jotlmsg'
    implementation 'jakarta.mail:jakarta.mail-api'
    implementation 'jakarta.activation:jakarta.activation-api'
    
    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

bootJar {
    archiveFileName = "nanfeng-outlook-${version}.jar"
}

// 复制资源文件
task copyResources(type: Copy) {
    from '../src/main/resources/aspose_total_20991231_license.xml'
    into 'src/main/resources'
}

processResources.dependsOn copyResources 