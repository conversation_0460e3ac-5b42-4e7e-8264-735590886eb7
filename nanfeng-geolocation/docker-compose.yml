services:
  nanfeng-geolocation:
    image: maven.bluesking.cn/nanfeng-geolocation:latest
    container_name: nanfeng-geolocation
    restart: unless-stopped
    network_mode: host
    environment:
      - JAVA_OPT=-Djasypt.encryptor.password=nanfeng-tools -Xms512m -Xmx2048m -XX:+UseZGC -Duser.timezone=Asia/Shanghai
      - SPRINGBOOT_OPT=--spring.profiles.active=prod --logging.config=/app/config/logback-spring.xml
      - SPRING_CONFIG_LOCATION=/app/config/application.properties
    volumes:
      - /opt/nanfeng-geolocation/data:/app/data
      - /opt/nanfeng-geolocation/config:/app/config
      - /opt/nanfeng-geolocation/logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
    ulimits:
      nofile:
        soft: 65535
        hard: 65535

volumes:
  data:
    driver: local
  config:
    driver: local
  logs:
    driver: local 