FROM openjdk:17-slim as builder
WORKDIR application
ARG JAR_FILE=build/libs/*.jar
COPY ${JAR_FILE} application.jar
RUN java -Djarmode=layertools -jar application.jar extract

FROM openjdk:17-slim
WORKDIR application

# 设置时区
ENV TIME_ZONE=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TIME_ZONE /etc/localtime && echo $TIME_ZONE > /etc/timezone

# JVM 和应用配置参数，可通过环境变量覆盖。
ENV JAVA_OPT="-Xms500m -Xmx2048m -Duser.timezone=Asia/Shanghai -Dsun.jnu.encoding=UTF-8 -Dfile.encoding=UTF-8"
ENV SPRINGBOOT_OPT="--spring.profiles.active=prod --logging.config=/app/config/logback-spring.xml"
ENV SPRING_CONFIG_LOCATION="optional:classpath:/application.properties,optional:file:/app/config/application.properties"
ENV LOG_PATH "/app/logs/"

# 从构建阶段复制分层的 jar 文件
COPY --from=builder application/dependencies/ ./
COPY --from=builder application/spring-boot-loader/ ./
COPY --from=builder application/snapshot-dependencies/ ./
COPY --from=builder application/application/ ./

# 声明数据卷
VOLUME ["/app/config", "/app/data", "/app/logs"]

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --retries=3 CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动命令
ENTRYPOINT ["sh", "-c", "java ${JAVA_OPT} -Dspring.config.location=${SPRING_CONFIG_LOCATION} org.springframework.boot.loader.JarLauncher ${SPRINGBOOT_OPT}"] 