package cn.bluesking.nanfeng.geolocation;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.PropertySource;

/**
 * 地理位置服务应用程序入口类。
 *
 * <AUTHOR>
 * @date 2025-07-01
 * @since 2.2.0
 */
@SpringBootApplication
@PropertySource(value = "file:${SPRING_CONFIG_LOCATION}", encoding = "UTF-8")
public class GeolocationApplication {

    /*-------------------- public static method --------------------*/

    public static void main(String[] args) {
        SpringApplication.run(GeolocationApplication.class, args);
    }

} 