apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'

dependencies {
    // 依赖基础模块
    implementation project(':nanfeng-common')
    implementation project(':nanfeng-common-web')
    implementation project(':nanfeng-http')
    
    // Spring Boot 依赖
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    
    // 数据库依赖
    implementation 'org.postgresql:postgresql'
    implementation 'org.flywaydb:flyway-database-postgresql'
    
    // 配置加密
    implementation 'com.github.ulisesbocchio:jasypt-spring-boot-starter'
    
    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
}

bootJar {
    archiveFileName = "nanfeng-geolocation-${version}.jar"
} 