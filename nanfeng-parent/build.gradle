plugins {
    id 'java-platform'
    id 'maven-publish'
}

group = 'cn.bluesking'
version = '2.2.0'

javaPlatform {
    allowDependencies()
}

dependencies {
    api platform('org.springframework.boot:spring-boot-dependencies:3.5.3')
    
    constraints {
        // 通用工具库
        api 'com.aventrix.jnanoid:jnanoid:2.0.0'
        api 'com.google.code.gson:gson:2.9.1'
        api 'com.google.guava:guava:33.4.8-jre'
        api 'com.squareup.okhttp3:okhttp:4.12.0'
        api 'com.jcraft:jsch:0.1.55'
        api 'commons-validator:commons-validator:1.9.0'
        api 'org.apache.commons:commons-lang3:3.12.0'
        
        // Excel 处理
        api 'org.apache.poi:poi:5.4.0'
        api 'org.apache.poi:poi-ooxml:5.4.0'
        api 'org.apache.poi:poi-scratchpad:5.4.0'
        
        // 反射和代码增强
        api 'org.javassist:javassist:3.29.2-GA'
        api 'org.reflections:reflections:0.10.2'
        
        // 邮件相关
        api 'org.simplejavamail:simple-java-mail:7.5.0'
        api 'ch.astorm:jotlmsg:2.0'
        api 'jakarta.mail:jakarta.mail-api:2.1.0'
        api 'jakarta.activation:jakarta.activation-api:2.1.0'
        
        // Web 相关
        api 'jakarta.servlet:jakarta.servlet-api:6.1.0'
        
        // 数据库相关
        api 'org.postgresql:postgresql'
        api 'org.hibernate.orm:hibernate-community-dialects:6.2.2.Final'
        api 'org.flywaydb:flyway-core'
        api 'org.flywaydb:flyway-database-postgresql'
        
        // 配置加密
        api 'com.github.ulisesbocchio:jasypt-spring-boot-starter:3.0.5'
        api 'org.jasypt:jasypt:1.9.3'
        
        // 代码质量工具
        api 'com.diffplug.spotless:spotless-plugin-gradle:6.21.0'
    }
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.javaPlatform
        }
    }
    repositories {
        maven {
            url = "${project.rootDir}/repo"
        }
    }
} 