apply plugin: 'java-library'
apply plugin: 'io.spring.dependency-management'

dependencies {
    // 依赖 nanfeng-common 模块
    api project(':nanfeng-common')
    
    // Spring Web 依赖
    api 'org.springframework:spring-web'
    api 'org.springframework:spring-webmvc'
    
    // Jackson 依赖
    api 'com.fasterxml.jackson.core:jackson-databind'
    
    // Servlet API
    compileOnly 'jakarta.servlet:jakarta.servlet-api'
    
    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

bootJar {
    enabled = false
}

jar {
    enabled = true
} 