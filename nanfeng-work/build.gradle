apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'

dependencies {
    // 依赖基础模块
    implementation project(':nanfeng-common')
    implementation project(':nanfeng-common-web')
    
    // Spring Boot 依赖
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    
    // 数据库依赖
    implementation 'org.postgresql:postgresql'
    implementation 'org.flywaydb:flyway-database-postgresql'
    
    // 配置加密
    implementation 'com.github.ulisesbocchio:jasypt-spring-boot-starter'
    
    // Excel 处理依赖
    implementation 'org.apache.poi:poi'
    implementation 'org.apache.poi:poi-ooxml'
    implementation 'org.apache.poi:poi-scratchpad'
    
    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

bootJar {
    archiveFileName = "nanfeng-work-${version}.jar"
} 